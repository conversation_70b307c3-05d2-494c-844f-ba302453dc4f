using UnityEngine;
using System.Collections;
using Holoville.HOTween;
using System;

public class PE_HomeScene : MonoBehaviour
{

    public GameObject noAdsIcon, btnPlay, titleObject;
    public AudioClip effectSound;
    private bool buttonHasClicked = false;
    private AudioSource myAudioSource;

    void Awake()
    {
        GlobalVariable.CheckUserOSLanguage();
        GlobalVariable.GetUserSavedLanguage();
        GameManager.Instance().GetSoundOnOff();

    }

    // Use this for initialization
    void Start()
    {
        myAudioSource = GetComponent<AudioSource>();
        myAudioSource.spatialBlend = 0;
        buttonHasClicked = false;

        //noAdsIcon.SetActive(false);

        if (!IAppsTeamIOSUntil.IOSCheckContentUnLock(GlobalVariable.IAP_ID1)) GameUtility.CheckHasConnectedToNetwork();



        if (IAppsTeamIOSUntil.IOSCheckContentUnLock(GlobalVariable.IAP_ID1))
            noAdsIcon.SetActive(true);
        else
            noAdsIcon.SetActive(false);


        GameManager.Instance().SaveRateMeCount();
        if (GameManager.Instance().GetRateMeCount() >= GetUserRateCount("Rate_police"))
        {
            IAppsTeamIOSUntil.RequestAppstoreReview(); 
        }

        TitleObject_MoveIn();

        DestroyAds();

        //noAdsIcon.SetActive(false);
    }

    void DestroyAds()
    {
        AdsBannerControl adsContrl = FindObjectOfType<AdsBannerControl>();
        if (adsContrl)
        {
            adsContrl.DestroyBanners();
        }
    }

    int GetUserRateCount(string key)
    {
        string strRate = String.Empty;
        #if UNITY_IPHONE
        strRate = IAppsTeamIOSUntil.GetConfigParams(key);
        #endif

        try
        {
            if (strRate == "" || strRate == null)
            {
                return 10;
            }
            else
            {
                int count = int.Parse(strRate);

                if (count < 0)
                    count = 0;
                if (count > 100)
                    count = 100;

                return count;
            }
        }
        catch
        {
            return 10;
        }

    }

    /*
    // Update is called once per frame
    void Update () {
    
    }
    */


    void GoToMap()
    {
        if (!buttonHasClicked)
        {
            buttonHasClicked = true;
            Debug.Log("PlayGameF");
            GameUtility.GoToSceneName("Map");
        }
    }

    void PlayGame()
    {
        if (!buttonHasClicked)
        {
            buttonHasClicked = true;
            Debug.Log("PlayGame");
            GameUtility.GoToSceneName("PE_Map");
        }

    }

    void SettingClick()
    {
        string msgTitle = GameUtility.LocalizedString("ForParents");
        int answer = 0;
        string msgContent = msgTitle + GameUtility.LocalizedString("ForParentsQT") + GameUtility.GetParentQuestion(out answer);
        string strA = GameUtility.LocalizedString("Cancel");
        string strB = GameUtility.LocalizedString("Submit");
        NativeDialogs.Instance.ShowPromptMessageBox("", msgContent, new string[] { strA, strB }, true,
                                                    (string prompt, string button) => {


                                                        if (answer.ToString() == prompt)
                                                        {
                                                            if (!buttonHasClicked)
                                                            {
                                                                buttonHasClicked = true;
                                                                IAppsTeamIOSUntil.MobClickEvent("SettingScene");
                                                                GameUtility.GoToSceneName("Setting");
                                                            }
                                                        }

                                                    });

    }

    void ShoppingClick()
    {
        string msgContent = GameUtility.LocalizedString("title2");
        string btn1 = GameUtility.LocalizedString("Cancel");
        string btn2 = GameUtility.LocalizedString("Submit");
        NativeDialogs.Instance.ShowMessageBox("", msgContent, new string[] { btn1, btn2 }, false, (string b) => {

            if (b == btn2)
            {
                GoToParentZoneClick();
            }

        });

    }

    void GoToParentZoneClick()
    {
        string msgTitle = GameUtility.LocalizedString("ForParents");
        int answer = 0;
        string msgContent = msgTitle + GameUtility.LocalizedString("ForParentsQT") + GameUtility.GetParentQuestion(out answer);
        string strA = GameUtility.LocalizedString("Cancel");
        string strB = GameUtility.LocalizedString("Submit");
        NativeDialogs.Instance.ShowPromptMessageBox("", msgContent, new string[] { strA, strB }, true,
        (string prompt, string button) => {


            if (answer.ToString() == prompt)
            {
                if (!buttonHasClicked)
                {
                    buttonHasClicked = true;
                    IAppsTeamIOSUntil.MobClickEvent("ShoppingBtnFromHome");
                    GameManager.Instance().SaveParentsZoneNewMsgFlag();
                    GameUtility.GoToSceneName("ParentsZone");
                }
            }

        });

    }

    void TitleObject_MoveIn()
    {
        TweenParms parms = new TweenParms();
        parms.Prop("position", new Vector3(0, 8.5f, 0));
        parms.AutoKill(true);
        parms.Ease(EaseType.EaseOutBack);
        parms.Loops(1, LoopType.Yoyo);
        parms.OnComplete(StartPlay);
        parms.Delay(1);
        HOTween.From(titleObject.transform, 0.6f, parms);

    }

    void StartPlay()
    {
        TweenParms parms = new TweenParms();
        parms.Prop("localScale", new Vector3(1.1f, 1.1f, 1.1f));
        parms.AutoKill(true);
        parms.Ease(EaseType.Linear);
        parms.Loops(-1, LoopType.Yoyo);
        parms.Delay(0);
        HOTween.To(btnPlay.transform, 1, parms);

        SoundManager.PlaySFX(myAudioSource, effectSound);
    }
}

