using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using Newtonsoft.Json.Linq;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;
using AIMGameFramework.Core.Interfaces;
using AIMGameFramework.UI;
using AMIGameFramework.Utils;

namespace AIMGameFramework
{
    /// <summary>
    /// 图片拼图游戏模板类
    /// 实现图片拼图游戏的核心逻辑
    /// </summary>
    public class PictureShapePuzzleTemplate : GameTemplateBase
    {
        [Header("调试设置")]
        [Tooltip("是否显示网格点和标记点（调试用）")]
        [SerializeField] private bool showDebugMarkers = true; // 调试开关，控制是否显示红色和绿色十字点

        [Header("拼图游戏设置")]
        [SerializeField] private Transform puzzleContainer;
        [SerializeField] private Transform piecesContainer;
        [SerializeField] private AudioSource audioSource;
        [SerializeField] private GameObject emptyFramePrefab;
        [SerializeField] private GameObject puzzlePiecePrefab;
        [SerializeField] private GameObject puzzleHolePrefab;

        [SerializeField] private GameObject policeExpressionsObject;



        // 配置数据
        private PictureShapePuzzleConfig puzzleConfig;

        // 游戏数据
        private List<PaintingData> selectedPaintings = new List<PaintingData>();
        private List<GameObject> paintingObjects = new List<GameObject>();
        private List<GameObject> pieceObjects = new List<GameObject>();
        private int currentPaintingIndex = 0;
        private int matchedCount = 0;
        private int totalPieces = 0;
        private bool showingEmptyFrame = false;

        // 绿色十字点位置数据
        private Dictionary<string, List<Vector2>> greenPointPositions = new Dictionary<string, List<Vector2>>();

        // 音效
        private AudioClip correctMatchSound;
        private AudioClip incorrectMatchSound;
        private AudioClip taskCompleteSound;
        private AudioClip taskFailedSound;
        private AudioClip missingPaintingSound;

        // UI元素
        private GameObject soundButton;

        // 事件
        public event Action<bool> PieceMatched;
        public event Action<int, int> PaintingCompleted;
        public event Action EmptyFrameShown;

        public List<GameObject> greenCrossMarkers = new List<GameObject>();

        #region 初始化方法



        /// <summary>
        /// 验证配置参数是否完整有效
        /// </summary>
        /// <param name="config">配置对象</param>
        /// <returns>验证结果，包含是否验证通过和错误信息</returns>
        private (bool isValid, string errorMessage) ValidateConfig(JObject config)
        {
            try
            {
                Debug.Log("开始验证图片拼图游戏配置参数");

                // 检查配置是否为空
                if (config == null)
                {
                    return (false, "配置对象为空");
                }

                // 检查必要的顶级字段
                if (config["templateId"] == null)
                {
                    return (false, "配置中缺少templateId字段");
                }

                string templateId = config["templateId"].ToString();
                if (templateId != "picture_shape_puzzle")
                {
                    return (false, $"模板ID不匹配: 期望 picture_shape_puzzle, 实际 {templateId}");
                }

                // 检查templateSpecificConfig字段
                if (config["templateSpecificConfig"] == null)
                {
                    return (false, "配置中缺少templateSpecificConfig字段");
                }

                JObject templateSpecificConfig = (JObject)config["templateSpecificConfig"];

                // 检查画作配置
                bool hasPaintings = templateSpecificConfig["paintings"] != null && templateSpecificConfig["paintings"].Type == JTokenType.Array;
                bool hasPaintingsConfigPath = templateSpecificConfig["paintingsConfigPath"] != null && !string.IsNullOrEmpty(templateSpecificConfig["paintingsConfigPath"].ToString());

                if (!hasPaintings && !hasPaintingsConfigPath)
                {
                    return (false, "配置中既没有直接定义paintings数组，也没有指定paintingsConfigPath");
                }

                // 检查是否有外部画作配置路径
                bool hasExternalPaintingsConfig = templateSpecificConfig["paintingsConfigPath"] != null &&
                                             !string.IsNullOrEmpty(templateSpecificConfig["paintingsConfigPath"].ToString());

                // 检查模板设置
                if (templateSpecificConfig["templateSettings"] != null)
                {
                    JObject templateSettings = (JObject)templateSpecificConfig["templateSettings"];

                    // 检查pieceSize
                    if (templateSettings["pieceSize"] != null)
                    {
                        JObject pieceSize = (JObject)templateSettings["pieceSize"];
                        if (pieceSize["width"] == null || pieceSize["height"] == null)
                        {
                            return (false, "templateSettings.pieceSize中缺少width或height字段");
                        }
                    }

                    // 检查其他必要的模板设置字段
                    string[] requiredTemplateSettings = { "missingPiecesCount", "pieceShape", "generateIncomplete", "positionMode" };
                    foreach (var field in requiredTemplateSettings)
                    {
                        if (templateSettings[field] == null)
                        {
                            return (false, $"templateSettings中缺少{field}字段");
                        }
                    }
                }
                else if (!hasExternalPaintingsConfig)
                {
                    // 只有在没有外部画作配置路径的情况下，才需要在主配置文件中包含templateSettings
                    Debug.LogWarning("配置中缺少templateSettings字段，将使用默认设置");
                    Debug.LogWarning("请在配置中添加templateSettings字段，详细信息请查看调试日志中的'templateSettings字段清单'");
                    // 输出templateSettings字段清单
                    LogTemplateSettingsRequirements();
                }
                else
                {
                    // 有外部画作配置路径，templateSettings应该在外部配置文件中
                    Debug.Log("配置中使用了外部画作配置路径，templateSettings应该在外部配置文件中");
                }

                // 检查UI元素配置
                if (templateSpecificConfig["uiElements"] != null)
                {
                    JObject uiElements = (JObject)templateSpecificConfig["uiElements"];

                    // 检查uiContainer
                    if (uiElements["uiContainer"] != null)
                    {
                        JObject uiContainer = (JObject)uiElements["uiContainer"];
                        if (uiContainer["name"] == null)
                        {
                            return (false, "uiElements.uiContainer中缺少name字段");
                        }
                    }
                    else
                    {
                        Debug.LogWarning("配置中缺少uiElements.uiContainer字段，将使用默认设置");
                    }
                }
                else
                {
                    Debug.LogWarning("配置中缺少uiElements字段，将使用默认设置");
                }

                Debug.Log("配置参数验证通过");
                return (true, null);
            }
            catch (Exception ex)
            {
                return (false, $"验证配置参数时发生异常: {ex.Message}");
            }
        }

        // 标记是否已创建参考文件
        private static bool hasCreatedReferenceFile = false;

        /// <summary>
        /// 输出templateSettings字段清单
        /// </summary>
        private void LogTemplateSettingsRequirements()
        {
            // 简化日志输出，只输出关键信息
            Debug.Log("=== templateSettings字段清单 ===");
            Debug.Log("必需字段: missingPiecesCount, pieceShape, pieceSize, generateIncomplete, positionMode");
            Debug.Log("可选字段: positionRadius");
            Debug.Log("注意: templateSettings应该在外部配置文件中，而不是主配置文件中");
            Debug.Log("=== 清单结束 ===");

            // 禁用文件创建功能
            // #if UNITY_EDITOR
            // if (!hasCreatedReferenceFile)
            // {
            //     CreateTemplateSettingsReferenceFile();
            //     hasCreatedReferenceFile = true;
            // }
            // #endif
        }

        #if UNITY_EDITOR
        /// <summary>
        /// 创建templateSettings参考文件
        /// </summary>
        private void CreateTemplateSettingsReferenceFile()
        {
            Debug.Log("开始创建templateSettings参考文件...");
            Debug.Log($"hasCreatedReferenceFile初始值: {hasCreatedReferenceFile}");

            try
            {
                // 使用绝对路径 - 保存到TestLogs目录，方便用户访问
                string absolutePath = Application.dataPath + "/LearningSuite/TestLogs";
                string fileName = "TemplateSettings参考.txt";
                string filePath = System.IO.Path.Combine(absolutePath, fileName);

                Debug.Log($"参考文件将保存到: {filePath}");

                // 创建文件内容
                string content = @"# PictureShapePuzzle模板 - templateSettings字段清单

## 配置文件位置信息

### 主配置文件
- **不需要包含templateSettings配置**
- 位置：`GameApp/Assets/LearningSuite/ThemeConfigs/Police/Assets/Resources/police_mission_art_gallery_case_1_1.json`
- 该文件通过`paintingsConfigPath`字段指向外部画作配置文件

### 外部画作配置文件
- **需要包含templateSettings配置**
- 位置：`GameApp/Assets/LearningSuite/ThemeConfigs/Police/Assets/Resources/scene1_1_paintings.json`
- 该文件包含画作详细信息和templateSettings配置

## 必需字段

1. **missingPiecesCount** (整数)
   - 描述：每幅画作的缺失部分数量
   - 默认值：2
   - 示例：`""""missingPiecesCount"""": 3`

2. **pieceShape** (字符串)
   - 描述：拼图块形状类型
   - 默认值：""""puzzle""""
   - 示例：`""""pieceShape"""": """"puzzle""""`

3. **pieceSize** (对象)
   - 描述：拼图块大小
   - 包含两个子字段：
     - **width** (数字)：宽度
     - **height** (数字)：高度
   - 默认值：`{""""width"""": 100, """"height"""": 100}`
   - 示例：`""""pieceSize"""": {""""width"""": 150, """"height"""": 150}`

4. **generateIncomplete** (布尔值)
   - 描述：是否自动生成不完整图像
   - 默认值：true
   - 示例：`""""generateIncomplete"""": true`

5. **positionMode** (字符串)
   - 描述：拼图块位置计算方式
   - 可选值：
     - """"circle""""：将拼图块均匀分布在圆周上，适合少量拼图块
     - """"grid""""：将拼图块排列在正方形网格中，整齐有序
     - """"random""""：在指定半径内随机分布拼图块，增加游戏难度
   - 默认值：""""circle""""
   - 示例：`""""positionMode"""": """"grid""""`

## 可选字段

6. **positionRadius** (数字)
   - 描述：拼图块位置半径
     - circle模式：确定圆的半径
     - grid模式：确定正方形区域的半边长（宽度和高度均为radius*2）
     - random模式：确定随机分布的最大距离
   - 默认值：150.0
   - 示例：`""""positionRadius"""": 150`

## 完整示例

```json
{
  """"templateSettings"""": {
    """"missingPiecesCount"""": 3,
    """"pieceShape"""": """"puzzle"""",
    """"pieceSize"""": {""""width"""": 150, """"height"""": 150},
    """"generateIncomplete"""": true,
    """"positionMode"""": """"grid"""",
    """"positionRadius"""": 150
  }
}
```

## 注意事项

1. 如果配置中缺少templateSettings字段，系统将使用默认设置。
2. 当您在配置文件中添加templateSettings字段时，建议包含所有必需字段，以避免使用默认值可能带来的不一致性。
3. 主配置文件不需要包含templateSettings字段，而外部画作配置文件需要包含此字段。
4. 如果缺少必要的配置参数，系统会在日志中输出警告信息，并创建此参考文件。

## 文件生成时间
{{0}}
";

                // 添加生成时间
                content = string.Format(content, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                // 确保目录存在
                string directory = System.IO.Path.GetDirectoryName(filePath);
                Debug.Log($"检查目录是否存在: {directory}");
                if (!System.IO.Directory.Exists(directory))
                {
                    Debug.Log($"目录不存在，正在创建目录: {directory}");
                    System.IO.Directory.CreateDirectory(directory);
                    Debug.Log($"目录创建完成: {directory}");
                }
                else
                {
                    Debug.Log($"目录已存在: {directory}");
                }

                // 检查文件是否已存在
                if (System.IO.File.Exists(filePath))
                {
                    Debug.Log($"文件已存在，将被覆盖: {filePath}");
                }

                // 写入文件
                Debug.Log($"正在写入文件: {filePath}");
                System.IO.File.WriteAllText(filePath, content);
                Debug.Log($"文件写入完成: {filePath}");

                // 刷新Unity资源数据库，使文件在编辑器中可见
                Debug.Log("正在刷新Unity资源数据库...");
                UnityEditor.AssetDatabase.Refresh();
                Debug.Log("Unity资源数据库刷新完成");

                Debug.Log($"=== 已成功创建templateSettings参考文件 ===");
                Debug.Log($"文件路径: {filePath}");
                Debug.Log($"请查看该文件以了解templateSettings配置详情");

                hasCreatedReferenceFile = true;
                Debug.Log($"hasCreatedReferenceFile设置为: {hasCreatedReferenceFile}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"创建templateSettings参考文件时发生异常: {ex.Message}");
                Debug.LogError($"异常堆栈: {ex.StackTrace}");

                // 尝试使用备用路径
                try
                {
                    Debug.Log("尝试使用备用路径创建文件...");
                    string backupPath = "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/Assets/LearningSuite/TestLogs/TemplateSettings参考.txt";

                    // 确保目录存在
                    string backupDir = System.IO.Path.GetDirectoryName(backupPath);
                    if (!System.IO.Directory.Exists(backupDir))
                    {
                        System.IO.Directory.CreateDirectory(backupDir);
                    }

                    // 创建备用内容，包含配置文件位置信息
                    string backupContent = @"# PictureShapePuzzle模板 - templateSettings字段清单

## 配置文件位置信息
- 主配置文件（不需要包含templateSettings配置）:
  GameApp/Assets/LearningSuite/ThemeConfigs/Police/Assets/Resources/police_mission_art_gallery_case_1_1.json

- 外部画作配置文件（需要包含templateSettings配置）:
  GameApp/Assets/LearningSuite/ThemeConfigs/Police/Assets/Resources/scene1_1_paintings.json

## 必需字段
- missingPiecesCount: 每幅画作的缺失部分数量，默认值: 2
- pieceShape: 拼图块形状类型，默认值: ""puzzle""
- pieceSize: 拼图块大小，包含width和height两个子字段，默认值: {""width"": 100, ""height"": 100}
- generateIncomplete: 是否自动生成不完整图像，默认值: true
- positionMode: 拼图块位置计算方式，可选值: ""circle"", ""grid"", ""random""，默认值: ""circle""

## 可选字段
- positionRadius: 拼图块位置半径，默认值: 150.0

## 注意事项
- 主配置文件不需要包含templateSettings字段
- 外部画作配置文件需要包含templateSettings字段
- 如果缺少必要的配置参数，系统会在日志中输出警告信息，并创建此参考文件

生成时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                    System.IO.File.WriteAllText(backupPath, backupContent);
                    Debug.Log($"已使用备用路径创建参考文件: {backupPath}");

                    hasCreatedReferenceFile = true;
                }
                catch (System.Exception backupEx)
                {
                    Debug.LogError($"使用备用路径创建文件也失败: {backupEx.Message}");
                }
            }
        }
        #endif

        /// <summary>
        /// 初始化模板
        /// </summary>
        public override bool Initialize(JObject config)
        {
            try
            {
                Debug.Log("开始初始化图片拼图游戏模板");

                // 不再输出templateSettings字段清单
                // LogTemplateSettingsRequirements();

                // 检查配置是否为空
                if (config == null)
                {
                    Debug.LogError("配置对象为空");
                    return false;
                }

                // 输出配置内容
                Debug.Log($"配置内容: {config.ToString(Newtonsoft.Json.Formatting.Indented)}");

                // 验证配置参数
                var (isValid, errorMessage) = ValidateConfig(config);
                if (!isValid)
                {
                    Debug.LogError($"配置参数验证失败: {errorMessage}");
                    return false;
                }

                // 解析配置
                puzzleConfig = ParseConfig(config);
                if (puzzleConfig == null)
                {
                    Debug.LogError("解析配置失败，puzzleConfig为空");
                    return false;
                }

                // 检查画作列表
                if (puzzleConfig.Paintings == null)
                {
                    Debug.LogError("解析配置后画作列表为空 (puzzleConfig.Paintings == null)");
                }
                else if (puzzleConfig.Paintings.Count == 0)
                {
                    Debug.LogError("解析配置后画作列表为空 (puzzleConfig.Paintings.Count == 0)");
                }
                else
                {
                    Debug.Log($"成功解析配置，画作数量: {puzzleConfig.Paintings.Count}");
                    foreach (var painting in puzzleConfig.Paintings)
                    {
                        Debug.Log($"画作: ID={painting.Id}, FullImage={painting.FullImage}");
                    }
                }

                // 设置模板ID和名称
                templateId = "picture_shape_puzzle";
                templateName = "图片拼图游戏";

                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"初始化图片拼图游戏模板失败: {ex.Message}\n{ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 解析配置
        /// </summary>
        private PictureShapePuzzleConfig ParseConfig(JObject jsonConfig)
        {
            try
            {
                Debug.Log("开始解析图片拼图游戏配置...");

                // 检查配置是否为空
                if (jsonConfig == null)
                {
                    Debug.LogWarning("JSON配置对象为空，将创建默认配置");
                    return CreateDefaultConfig();
                }

                // 检查配置中是否包含必要的字段
                if (jsonConfig["templateSpecificConfig"] == null)
                {
                    Debug.LogWarning("配置中缺少templateSpecificConfig字段");
                }

                // 验证配置参数的完整性
                var (isValid, errorMessage) = ValidateConfig(jsonConfig);
                if (!isValid)
                {
                    Debug.LogWarning($"配置参数验证失败: {errorMessage}，将使用默认配置或尝试修复问题");
                    // 这里不直接返回默认配置，而是继续尝试解析，如果失败再返回默认配置
                }

                // 直接将JSON转换为配置对象
                var puzzleConfig = jsonConfig.ToObject<PictureShapePuzzleConfig>();

                if (puzzleConfig == null)
                {
                    Debug.LogError("配置转换失败，puzzleConfig为空");
                    return CreateDefaultConfig();
                }

                // 处理templateSpecificConfig中的uiElements
                if (jsonConfig["templateSpecificConfig"] != null && jsonConfig["templateSpecificConfig"]["uiElements"] != null)
                {
                    Debug.Log("找到templateSpecificConfig.uiElements配置，准备处理");

                    // 如果UIElements为空，创建一个新的实例
                    if (puzzleConfig.UIElements == null)
                    {
                        puzzleConfig.UIElements = new UIElementsConfig();
                        Debug.Log("创建新的UIElementsConfig实例");
                    }

                    // 处理uiContainer
                    if (jsonConfig["templateSpecificConfig"]["uiElements"]["uiContainer"] != null)
                    {
                        Debug.Log("处理uiContainer配置");
                        puzzleConfig.UIElements.UIContainer = jsonConfig["templateSpecificConfig"]["uiElements"]["uiContainer"].ToObject<UIContainerData>();
                        Debug.Log($"UIContainer配置处理完成，名称: {puzzleConfig.UIElements.UIContainer?.Name}");
                    }
                    else
                    {
                        Debug.LogWarning("未找到uiContainer配置");
                    }

                    // 处理soundButton
                    if (jsonConfig["templateSpecificConfig"]["uiElements"]["soundButton"] != null)
                    {
                        Debug.Log("处理soundButton配置");
                        puzzleConfig.UIElements.SoundButton = jsonConfig["templateSpecificConfig"]["uiElements"]["soundButton"].ToObject<UIElementData>();
                    }

                    // 处理policeExpressions
                    if (jsonConfig["templateSpecificConfig"]["uiElements"]["policeExpressions"] != null)
                    {
                        Debug.Log("处理policeExpressions配置");
                        puzzleConfig.UIElements.PoliceExpressions = jsonConfig["templateSpecificConfig"]["uiElements"]["policeExpressions"].ToObject<PoliceExpressionsData>();
                    }

                    Debug.Log("templateSpecificConfig.uiElements处理完成");
                }
                else
                {
                    Debug.LogWarning("未找到templateSpecificConfig.uiElements配置");
                }

                Debug.Log($"配置转换结果: PaintingsConfigPath={puzzleConfig.PaintingsConfigPath}, " +
                          $"RandomSelection={puzzleConfig.RandomSelection}, " +
                          $"PaintingsPerSession={puzzleConfig.PaintingsPerSession}, " +
                          $"FinalEmptyFrame={puzzleConfig.FinalEmptyFrame}, " +
                          $"UIElements={puzzleConfig.UIElements != null}");

                // 检查画作列表
                if (puzzleConfig.Paintings == null)
                {
                    Debug.LogWarning("配置中没有直接定义画作列表 (puzzleConfig.Paintings == null)");
                }
                else
                {
                    Debug.Log($"配置中直接定义了 {puzzleConfig.Paintings.Count} 幅画作");
                }

                // 如果配置中包含外部画作配置路径，加载外部配置
                if (!string.IsNullOrEmpty(puzzleConfig.PaintingsConfigPath))
                {
                    Debug.Log($"检测到外部画作配置路径: {puzzleConfig.PaintingsConfigPath}，开始加载外部配置");
                    LoadExternalPaintingsConfig(puzzleConfig);
                }
                else
                {
                    Debug.LogWarning("配置中没有指定外部画作配置路径，将使用直接定义的画作列表");
                }

                // 最终检查
                if (puzzleConfig.Paintings == null)
                {
                    Debug.LogError("解析配置后画作列表仍为空 (puzzleConfig.Paintings == null)");
                    // 初始化一个空列表，避免空引用异常
                    puzzleConfig.Paintings = new List<PaintingData>();
                }

                if (puzzleConfig.Paintings.Count == 0)
                {
                    Debug.LogError("解析配置后画作列表为空 (puzzleConfig.Paintings.Count == 0)");
                }

                return puzzleConfig;
            }
            catch (Exception ex)
            {
                Debug.LogError($"解析图片拼图游戏配置失败: {ex.Message}\n{ex.StackTrace}");
                return null;
            }
        }

        /// <summary>
        /// 加载外部画作配置
        /// </summary>
        private void LoadExternalPaintingsConfig(PictureShapePuzzleConfig config)
        {
            try
            {
                Debug.Log($"开始加载外部画作配置，路径: {config.PaintingsConfigPath}");

                // 检查路径是否为空
                if (string.IsNullOrEmpty(config.PaintingsConfigPath))
                {
                    Debug.LogError("外部画作配置路径为空");
                    return;
                }

                // 移除扩展名
                string resourcePath = RemoveExtension(config.PaintingsConfigPath);
                Debug.Log($"处理后的资源路径: {resourcePath}");

                // 使用准确的配置文件路径
                // 在Unity中，Resources.Load不需要包含"Resources/"前缀和文件扩展名
                string exactPath = resourcePath;
                Debug.Log($"使用准确的配置文件路径: {exactPath}");

                // 加载配置文件
                TextAsset configAsset = Resources.Load<TextAsset>(exactPath);

                if (configAsset == null)
                {
                    Debug.LogError($"无法加载画作配置文件: {exactPath}");
                    Debug.LogError($"请确保配置文件位于Resources文件夹中，并且路径正确");

                    // 输出当前可用的资源，帮助调试
                    Debug.Log("列出Resources目录下的所有TextAsset资源...");
                    TextAsset[] allTextAssets = Resources.LoadAll<TextAsset>("");
                    Debug.Log($"找到 {allTextAssets.Length} 个TextAsset资源:");
                    foreach (var asset in allTextAssets)
                    {
                        Debug.Log($"  - {asset.name}");
                    }

                    return;
                }

                Debug.Log($"配置文件内容: {configAsset.text}");

                // 解析JSON
                JObject externalConfig;
                try
                {
                    externalConfig = JObject.Parse(configAsset.text);
                    Debug.Log("成功解析JSON配置");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"解析JSON配置失败: {ex.Message}");
                    return;
                }

                // 检查配置结构
                var propertyNames = externalConfig.Properties().Select(p => p.Name).ToList();
                Debug.Log($"外部配置包含以下字段: {string.Join(", ", propertyNames)}");

                // 验证外部配置参数
                bool isValid = ValidateExternalConfig(externalConfig);
                if (!isValid)
                {
                    Debug.LogWarning("外部配置参数验证失败，但将继续尝试加载");
                }

                /// <summary>
                /// 验证外部配置参数
                /// </summary>
                bool ValidateExternalConfig(JObject config)
                {
                    try
                    {
                        // 检查paintings字段
                        if (config["paintings"] == null)
                        {
                            Debug.LogWarning("外部配置中缺少paintings字段");
                            return false;
                        }

                        if (config["paintings"].Type != JTokenType.Array)
                        {
                            Debug.LogWarning($"外部配置中paintings字段不是数组，而是 {config["paintings"].Type}");
                            return false;
                        }

                        JArray paintingsArray = (JArray)config["paintings"];
                        if (paintingsArray.Count == 0)
                        {
                            Debug.LogWarning("外部配置中paintings数组为空");
                            return false;
                        }

                        // 检查templateSettings字段
                        if (config["templateSettings"] != null)
                        {
                            JObject templateSettings = (JObject)config["templateSettings"];

                            // 检查必要的模板设置字段
                            string[] requiredFields = { "missingPiecesCount", "pieceShape", "generateIncomplete" };
                            foreach (var field in requiredFields)
                            {
                                if (templateSettings[field] == null)
                                {
                                    Debug.LogWarning($"外部配置中templateSettings缺少{field}字段");
                                }
                            }

                            // 检查pieceSize
                            if (templateSettings["pieceSize"] != null)
                            {
                                JObject pieceSize = (JObject)templateSettings["pieceSize"];
                                if (pieceSize["width"] == null || pieceSize["height"] == null)
                                {
                                    Debug.LogWarning("外部配置中templateSettings.pieceSize缺少width或height字段");
                                }
                            }
                            else
                            {
                                Debug.LogWarning("外部配置中templateSettings缺少pieceSize字段");
                            }
                        }
                        else
                        {
                            Debug.LogWarning("外部配置中缺少templateSettings字段");
                            Debug.LogWarning("请在外部配置文件中添加templateSettings字段");
                            // 不再输出templateSettings字段清单
                            // LogTemplateSettingsRequirements();
                        }

                        return true;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"验证外部配置参数时发生异常: {ex.Message}");
                        return false;
                    }
                }

                // 加载画作
                if (externalConfig["paintings"] != null)
                {
                    Debug.Log($"找到画作配置，类型: {externalConfig["paintings"].Type}");

                    if (externalConfig["paintings"].Type == JTokenType.Array)
                    {
                        JArray paintingsArray = (JArray)externalConfig["paintings"];
                        Debug.Log($"画作配置是数组，包含 {paintingsArray.Count} 个元素");

                        try
                        {
                            config.Paintings = externalConfig["paintings"].ToObject<List<PaintingData>>();
                            Debug.Log($"成功转换画作配置，共 {config.Paintings.Count} 幅画作");

                            // 输出画作详情
                            for (int i = 0; i < config.Paintings.Count; i++)
                            {
                                var painting = config.Paintings[i];
                                Debug.Log($"画作[{i}]: ID={painting.Id}, FullImage={painting.FullImage}");
                            }


                            // 处理简化的画作配置
                            // 从externalConfig中读取templateSettings
                            if (externalConfig["templateSettings"] != null)
                            {
                                Debug.Log("找到templateSettings配置");
                                Debug.Log($"templateSettings JSON: {externalConfig["templateSettings"].ToString()}");

                                try
                                {
                                    // 将templateSettings转换为TemplateSettings对象
                                    var templateSettings = externalConfig["templateSettings"].ToObject<TemplateSettings>();
                                    if (templateSettings != null)
                                    {
                                        Debug.Log($"成功读取templateSettings: missingPiecesCount={templateSettings.MissingPiecesCount}, pieceSize={templateSettings.PieceSize}");

                                        // 检查pieceSize是否正确解析
                                        if (templateSettings.PieceSize == null || (templateSettings.PieceSize.x == 0 && templateSettings.PieceSize.y == 0))
                                        {
                                            Debug.LogWarning("pieceSize解析失败或为零，尝试手动解析");

                                            // 尝试手动解析pieceSize
                                            if (externalConfig["templateSettings"]["pieceSize"] != null)
                                            {
                                                var pieceSizeObj = externalConfig["templateSettings"]["pieceSize"];
                                                float width = pieceSizeObj["width"]?.Value<float>() ?? 100;
                                                float height = pieceSizeObj["height"]?.Value<float>() ?? 100;
                                                templateSettings.PieceSize = new Vector2(width, height);
                                                Debug.Log($"手动解析pieceSize成功: width={width}, height={height}");
                                            }
                                        }

                                        config.TemplateSettings = templateSettings;
                                        Debug.Log($"设置config.TemplateSettings完成，pieceSize={config.TemplateSettings.PieceSize}");
                                    }
                                    else
                                    {
                                        Debug.LogError("templateSettings转换结果为null");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Debug.LogError($"转换templateSettings失败: {ex.Message}\n{ex.StackTrace}");

                                    // 尝试手动创建templateSettings
                                    try
                                    {
                                        var templateSettings = new TemplateSettings();

                                        // 尝试手动解析各个字段
                                        if (externalConfig["templateSettings"]["missingPiecesCount"] != null)
                                        {
                                            templateSettings.MissingPiecesCount = externalConfig["templateSettings"]["missingPiecesCount"].Value<int>();
                                        }

                                        if (externalConfig["templateSettings"]["pieceShape"] != null)
                                        {
                                            templateSettings.PieceShape = externalConfig["templateSettings"]["pieceShape"].Value<string>();
                                        }

                                        if (externalConfig["templateSettings"]["pieceSize"] != null)
                                        {
                                            var pieceSizeObj = externalConfig["templateSettings"]["pieceSize"];
                                            float width = pieceSizeObj["width"]?.Value<float>() ?? 100;
                                            float height = pieceSizeObj["height"]?.Value<float>() ?? 100;
                                            templateSettings.PieceSize = new Vector2(width, height);
                                        }

                                        if (externalConfig["templateSettings"]["generateIncomplete"] != null)
                                        {
                                            templateSettings.GenerateIncomplete = externalConfig["templateSettings"]["generateIncomplete"].Value<bool>();
                                        }

                                        if (externalConfig["templateSettings"]["positionMode"] != null)
                                        {
                                            templateSettings.PositionMode = externalConfig["templateSettings"]["positionMode"].Value<string>();
                                        }

                                        if (externalConfig["templateSettings"]["positionRadius"] != null)
                                        {
                                            templateSettings.PositionRadius = externalConfig["templateSettings"]["positionRadius"].Value<float>();
                                        }

                                        config.TemplateSettings = templateSettings;
                                        Debug.Log($"手动创建templateSettings成功: missingPiecesCount={templateSettings.MissingPiecesCount}, pieceSize={templateSettings.PieceSize}");
                                    }
                                    catch (Exception ex2)
                                    {
                                        Debug.LogError($"手动创建templateSettings失败: {ex2.Message}");
                                    }
                                }
                            }

                            // 处理简化的画作配置
                            ProcessSimplifiedPaintings(config);
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"转换画作配置失败: {ex.Message}");
                        }
                    }
                    else
                    {
                        Debug.LogError($"画作配置不是数组，而是 {externalConfig["paintings"].Type}");
                    }
                }
                else
                {
                    Debug.LogError("外部配置中没有找到paintings字段");
                }

                // 加载空画框
                if (externalConfig["emptyFrame"] != null)
                {
                    Debug.Log("找到空画框配置");
                    try
                    {
                        config.EmptyFrame = externalConfig["emptyFrame"].ToObject<EmptyFrameData>();
                        Debug.Log("成功加载空画框配置");
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"加载空画框配置失败: {ex.Message}");
                    }
                }

                // 加载云集成配置（如果有）
                if (externalConfig["cloudIntegration"] != null)
                {
                    // 处理云集成配置
                    Debug.Log("找到云集成配置");
                }

                // 最终检查
                if (config.Paintings == null)
                {
                    Debug.LogError("加载外部配置后画作列表仍为空 (config.Paintings == null)");
                    // 初始化一个空列表，避免空引用异常
                    config.Paintings = new List<PaintingData>();
                }

                if (config.Paintings.Count == 0)
                {
                    Debug.LogError("加载外部配置后画作列表为空 (config.Paintings.Count == 0)");
                }
                else
                {
                    Debug.Log($"成功加载画作配置，共 {config.Paintings.Count} 幅画作");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载外部画作配置失败: {ex.Message}\n{ex.StackTrace}");

                // 确保Paintings不为null
                if (config.Paintings == null)
                {
                    config.Paintings = new List<PaintingData>();
                }
            }
        }

        /// <summary>
        /// 处理简化的画作配置
        /// </summary>
        private void ProcessSimplifiedPaintings(PictureShapePuzzleConfig config)
        {
            // 获取模板设置
            var templateSettings = config.TemplateSettings;
            if (templateSettings == null)
            {
                Debug.Log("未找到模板设置，使用默认设置");
                templateSettings = new TemplateSettings
                {
                    MissingPiecesCount = 2,
                    PieceShape = "puzzle",
                    PieceSize = new Vector2(100, 100),
                    GenerateIncomplete = true,
                    PositionMode = "circle",
                    PositionRadius = 150f
                };
                config.TemplateSettings = templateSettings;
            }

            // 处理每个画作
            foreach (var painting in config.Paintings)
            {
                // 如果没有名称，使用ID作为默认名称
                if (painting.Name == null)
                {
                    painting.Name = new Dictionary<string, string>
                    {
                        { "zh_CN", painting.Id },
                        { "zh_TW", painting.Id },
                        { "en", painting.Id }
                    };
                }

                // 如果没有缺失部分，根据模板设置生成
                if (painting.MissingPieces == null || painting.MissingPieces.Count == 0)
                {
                    Debug.Log($"画作 {painting.Id} 在ProcessSimplifiedPaintings中没有缺失部分，将根据模板设置生成");

                    // 生成缺失部分
                    // 不再使用PositionRadius参数，但保留参数传递以保持方法签名兼容性
                    painting.MissingPieces = GenerateMissingPieces(
                        painting.Id,
                        templateSettings.MissingPiecesCount,
                        templateSettings.PositionMode,
                        0f);

                    Debug.Log($"在ProcessSimplifiedPaintings中为画作 {painting.Id} 生成了 {painting.MissingPieces.Count} 个缺失部分");
                }
            }
        }

        /// <summary>
        /// 生成缺失部分
        /// </summary>
        private List<PaintingPieceData> GenerateMissingPieces(string paintingId, int count, string positionMode, float radius)
        {
            List<PaintingPieceData> pieces = new List<PaintingPieceData>();

            // 计算画布中心
            Vector2 center = Vector2.zero; // 画布中心

            // 简化为只使用网格中心点的方式，忽略positionMode和radius参数
            Debug.Log($"使用简化的网格中心点方式生成 {count} 个拼图块位置");

            // 计算网格的行列数 - 尽量接近正方形布局
            int cols = Mathf.CeilToInt(Mathf.Sqrt(count));
            int rows = Mathf.CeilToInt((float)count / cols);

            Debug.Log($"网格布局: {rows}行 x {cols}列");

            // 使用固定的网格大小，不再依赖radius参数
            float gridWidth = 300f; // 固定网格宽度
            float gridHeight = 300f; // 固定网格高度

            float cellWidth = gridWidth / cols;
            float cellHeight = gridHeight / rows;

            // 计算起始位置（左上角）
            float startX = center.x - gridWidth / 2;
            float startY = center.y - gridHeight / 2;

            for (int i = 0; i < count; i++)
            {
                int row = i / cols;
                int col = i % cols;

                // 计算网格中心点位置
                float x = startX + col * cellWidth + cellWidth / 2;
                float y = startY + row * cellHeight + cellHeight / 2;

                Debug.Log($"拼图块 {i + 1} 位置: ({x}, {y})，所在单元格: 行={row + 1}, 列={col + 1}");

                // 创建缺失部分数据
                PaintingPieceData piece = new PaintingPieceData
                {
                    Id = $"{paintingId}_piece_{i + 1}",
                    Position = new Vector2(x, y)
                };

                pieces.Add(piece);
            }

            return pieces;
        }

        /// <summary>
        /// 生成拼图块ID
        /// </summary>
        private string GeneratePieceId(string paintingId, int pieceIndex)
        {
            // 生成拼图块的唯一ID
            // 例如：painting_1_piece_1
            string basePath = paintingId.Contains("_") ? paintingId.Substring(0, paintingId.LastIndexOf('_')) : paintingId;
            return $"{basePath}_piece_{pieceIndex}";
        }

        /// <summary>
        /// 从GreenPoint名称中提取piece ID
        /// </summary>
        /// <param name="greenPointName">GreenPoint名称，例如：GreenPoint_painting_1_piece_1</param>
        /// <returns>piece ID，例如：piece_1</returns>
        private string ExtractPieceIdFromGreenPoint(string greenPointName)
        {
            if (string.IsNullOrEmpty(greenPointName) || !greenPointName.StartsWith("GreenPoint_"))
            {
                Debug.LogWarning($"无效的GreenPoint名称: {greenPointName}");
                return string.Empty;
            }

            // 移除前缀 "GreenPoint_"
            string fullId = greenPointName.Replace("GreenPoint_", "");

            // 查找最后一个 "_piece_" 的位置
            int pieceIndex = fullId.LastIndexOf("_piece_");
            if (pieceIndex >= 0 && pieceIndex + 7 < fullId.Length)
            {
                // 提取 "piece_X" 部分
                string pieceId = fullId.Substring(pieceIndex + 1); // +1 跳过第一个下划线
                Debug.Log($"从 {greenPointName} 提取到piece ID: {pieceId}");
                return pieceId;
            }

            Debug.LogWarning($"无法从 {greenPointName} 提取piece ID");
            return string.Empty;
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private PictureShapePuzzleConfig CreateDefaultConfig()
        {
            Debug.Log("创建默认图片拼图游戏配置");

            // 创建默认配置对象
            var config = new PictureShapePuzzleConfig
            {
                RandomSelection = true,
                PaintingsPerSession = 1,
                FinalEmptyFrame = false,
                Paintings = new List<PaintingData>(),
                TemplateSettings = new TemplateSettings
                {
                    MissingPiecesCount = 2,
                    PieceShape = "puzzle",
                    PieceSize = new Vector2(100, 100),
                    GenerateIncomplete = true,
                    PositionMode = "grid",
                    PositionRadius = 150f
                },
                UIElements = new UIElementsConfig
                {
                    UIContainer = new UIContainerData
                    {
                        Name = "UIContainer"
                    },
                    SoundButton = new UIElementData
                    {
                        Position = new Vector2(750, 50),
                        Scale = 1.0f
                    },
                    PoliceExpressions = new PoliceExpressionsData
                    {
                        Correct = "ThemeConfigs/Police/Assets/UI/police_happy.png",
                        Incorrect = "ThemeConfigs/Police/Assets/UI/police_confused.png",
                        EndingExpression = "ThemeConfigs/Police/Assets/UI/police_happy.png",
                        Position = new Vector2(650, 150)
                    }
                }
            };

            Debug.Log("默认配置创建完成");
            return config;
        }

        /// <summary>
        /// 同步初始化模板
        /// </summary>
        public override IEnumerator InitializeAsync(JObject config, Action<bool> callback)
        {
            Debug.Log("PictureShapePuzzleTemplate.InitializeAsync 开始执行");

            // 检查配置是否为空
            if (config == null)
            {
                Debug.LogError("配置对象为空");
                callback?.Invoke(false);
                yield break;
            }

            // 验证配置参数
            var (isValid, errorMessage) = ValidateConfig(config);
            if (!isValid)
            {
                Debug.LogError($"配置参数验证失败: {errorMessage}");
                callback?.Invoke(false);
                yield break;
            }

            // 继续初始化过程
            bool success = Initialize(config);
            Debug.Log($"PictureShapePuzzleTemplate.Initialize 执行结果: {success}");

            if (success)
            {
                Debug.Log("准备执行 LoadResources");
                // 同步加载资源
                LoadResources();
                Debug.Log("LoadResources 执行完成");
            }
            else
            {
                Debug.LogError("初始化失败，跳过资源加载");
            }

            callback?.Invoke(success);
            Debug.Log($"InitializeAsync 执行完成，回调结果: {success}");
            yield break; // 直接返回，不再使用异步
        }



        /// <summary>
        /// 结束效果
        /// </summary>
        [Header("结束效果")]
        [Tooltip("游戏结束时显示的彩带效果对象")]
        [SerializeField] private GameObject ribbonEndingEffect;

        /// <summary>
        /// 同步加载资源
        /// </summary>
        protected void LoadResources()
        {
            Debug.Log("LoadResources 开始加载图片拼图游戏资源");

            // 跳过音效加载
            Debug.Log("跳过音效加载步骤");
            // LoadAudioClipsSync();

            // 加载UI元素
            Debug.Log("准备执行 LoadUIElementsSync");
            LoadUIElementsSync();
            Debug.Log("LoadUIElementsSync 执行完成");

            Debug.Log("准备初始化警察表情对象");
            InitPoliceExpressionObjects();
            Debug.Log("警察表情对象初始化完成");

            // 加载完成后设置模板状态为Ready
            currentTemplateState = TemplateState.Ready;
            Debug.Log($"模板状态已设置为: {currentTemplateState}，等待StartGame调用");

            Debug.Log("LoadResources 图片拼图游戏资源加载完成");
        }

        /// <summary>
        /// 异步加载资源 - 保留以兼容基类
        /// </summary>
        protected override IEnumerator LoadResourcesAsync()
        {
            Debug.Log("LoadResourcesAsync 被调用，但我们使用同步方法代替");
            LoadResources();
            yield break;
        }

        /// <summary>
        /// 加载音效
        /// </summary>
        private IEnumerator LoadAudioClips()
        {
            Debug.Log("LoadAudioClips 开始执行");

            if (puzzleConfig == null)
            {
                Debug.LogError("puzzleConfig 为空，无法加载音效");
                yield break;
            }

            try
            {
                if (puzzleConfig.AudioEffects != null)
                {
                    Debug.Log($"puzzleConfig.AudioEffects 不为空，包含 {puzzleConfig.AudioEffects.Count} 个音效");

                    // 加载正确匹配音效
                    if (puzzleConfig.AudioEffects.TryGetValue("correctMatch", out string correctMatchPath))
                    {
                        Debug.Log($"找到正确匹配音效路径: {correctMatchPath}");
                        // 在实际项目中，这里应该使用资源加载系统加载音效
                        // correctMatchSound = LoadAudioClip(correctMatchPath);
                    }

                    // 加载错误匹配音效
                    if (puzzleConfig.AudioEffects.TryGetValue("incorrectMatch", out string incorrectMatchPath))
                    {
                        Debug.Log($"找到错误匹配音效路径: {incorrectMatchPath}");
                        // incorrectMatchSound = LoadAudioClip(incorrectMatchPath);
                    }

                    // 加载任务完成音效
                    if (puzzleConfig.AudioEffects.TryGetValue("taskComplete", out string taskCompletePath))
                    {
                        Debug.Log($"找到任务完成音效路径: {taskCompletePath}");
                        // taskCompleteSound = LoadAudioClip(taskCompletePath);
                    }

                    // 加载任务失败音效
                    if (puzzleConfig.AudioEffects.TryGetValue("taskFailed", out string taskFailedPath))
                    {
                        Debug.Log($"找到任务失败音效路径: {taskFailedPath}");
                        // taskFailedSound = LoadAudioClip(taskFailedPath);
                    }

                    // 加载缺失画作音效
                    if (puzzleConfig.AudioEffects.TryGetValue("missingPainting", out string missingPaintingPath))
                    {
                        Debug.Log($"找到缺失画作音效路径: {missingPaintingPath}");
                        // missingPaintingSound = LoadAudioClip(missingPaintingPath);
                    }
                }
                else
                {
                    Debug.LogWarning("puzzleConfig.AudioEffects 为空，跳过音效加载");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"LoadAudioClips 执行过程中发生异常: {ex.Message}");
                Debug.LogError($"异常堆栈: {ex.StackTrace}");
            }

            Debug.Log("LoadAudioClips 执行完成");
            yield return null;
        }

        /// <summary>
        /// 同步加载UI元素
        /// </summary>
        private void LoadUIElementsSync()
        {
            Debug.Log("LoadUIElementsSync 开始加载图片拼图游戏UI元素");

            if (puzzleConfig == null)
            {
                Debug.LogError("puzzleConfig 为空，无法加载UI元素");
                return;
            }

            Debug.Log($"puzzleConfig不为空，检查UIElements: {puzzleConfig.UIElements != null}");

            if (puzzleConfig.UIElements != null)
            {
                Debug.Log("puzzleConfig.UIElements 不为空，开始加载UI元素");

                // 检查UIContainer是否存在
                Debug.Log($"检查UIContainer: {puzzleConfig.UIElements.UIContainer != null}");

                if (puzzleConfig.UIElements.UIContainer != null)
                {
                    Debug.Log($"UIContainer不为空，检查Name: '{puzzleConfig.UIElements.UIContainer.Name}'");
                }

                // 查找或创建UIContainer
                if (puzzleConfig.UIElements.UIContainer != null && !string.IsNullOrEmpty(puzzleConfig.UIElements.UIContainer.Name))
                {
                    string uiContainerName = puzzleConfig.UIElements.UIContainer.Name;
                    Debug.Log($"找到UIContainer配置，名称: {uiContainerName}");

                    // 查找场景中是否已存在UIContainer
                    GameObject uiContainer = GameObject.Find(uiContainerName);
                    if (uiContainer != null)
                    {
                        Debug.Log($"在场景中找到UIContainer: {uiContainerName}");
                    }
                    else
                    {
                        Debug.LogWarning($"在场景中找不到UIContainer: {uiContainerName}，将创建一个新的");

                        // 创建UIContainer
                        uiContainer = new GameObject(uiContainerName);

                        // 设置UIContainer的父对象为当前游戏对象
                        uiContainer.transform.SetParent(transform);

                        // 重置位置
                        uiContainer.transform.localPosition = Vector3.zero;

                        Debug.Log($"已创建新的UIContainer: {uiContainerName}");
                    }
                }
                else
                {
                    Debug.LogWarning("puzzleConfig.UIElements.UIContainer 为空或名称为空，跳过UIContainer查找/创建");
                }

                // 创建声音按钮
                if (puzzleConfig.UIElements.SoundButton != null)
                {
                    Debug.Log("找到声音按钮配置，准备创建声音按钮UI");
                    // 在实际项目中，这里应该创建声音按钮UI
                    // soundButton = CreateSoundButton(puzzleConfig.UIElements.SoundButton);
                }
                else
                {
                    Debug.LogWarning("puzzleConfig.UIElements.SoundButton 为空，跳过声音按钮创建");
                }
            }
            else
            {
                Debug.LogWarning("puzzleConfig.UIElements 为空，跳过UI元素加载");
            }

            Debug.Log("LoadUIElementsSync 执行完成");
        }



        #endregion

        #region 游戏流程方法

        /// <summary>
        /// 加载关卡
        /// </summary>
        public bool LoadLevel(string levelId)
        {
            try
            {
                Debug.Log($"加载图片拼图游戏关卡: {levelId}");

                // 清理之前的游戏内容
                ClearGameContent();

                // 选择画作
                SelectPaintings();

                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载图片拼图游戏关卡失败: {ex.Message}");
                return false;
            }
        }



        /// <summary>
        /// 选择画作
        /// </summary>
        private void SelectPaintings()
        {
            Debug.Log("开始选择画作...");

            // 检查配置是否存在
            if (puzzleConfig == null)
            {
                Debug.LogError("拼图配置为空，无法选择画作");
                return;
            }

            // 检查画作列表是否存在
            if (puzzleConfig.Paintings == null)
            {
                Debug.LogError("画作列表为空 (puzzleConfig.Paintings == null)");
                return;
            }

            // 检查画作数量
            if (puzzleConfig.Paintings.Count == 0)
            {
                Debug.LogError("没有可用的画作 (puzzleConfig.Paintings.Count == 0)");

                // 检查PaintingsConfigPath是否正确
                if (!string.IsNullOrEmpty(puzzleConfig.PaintingsConfigPath))
                {
                    Debug.LogError($"外部画作配置路径: {puzzleConfig.PaintingsConfigPath}，但未能加载任何画作");
                }

                return;
            }

            // 输出所有可用画作的详细信息
            Debug.Log($"可用画作列表 ({puzzleConfig.Paintings.Count}幅):");
            for (int i = 0; i < puzzleConfig.Paintings.Count; i++)
            {
                var painting = puzzleConfig.Paintings[i];

                // 检查并初始化MissingPieces
                if (painting.MissingPieces == null || painting.MissingPieces.Count == 0)
                {
                    // 获取模板设置
                    int missingPiecesCount = puzzleConfig.TemplateSettings?.MissingPiecesCount ?? 3;
                    string positionMode = puzzleConfig.TemplateSettings?.PositionMode ?? "grid";
                    // 不再使用radius参数，但保留参数传递以保持方法签名兼容性
                    float radius = 0f;

                    // 生成缺失部分
                    painting.MissingPieces = GenerateMissingPieces(painting.Id, missingPiecesCount, positionMode, radius);

                    Debug.Log($"为画作 {painting.Id} 生成了 {painting.MissingPieces.Count} 个缺失部分");
                }

                Debug.Log($"  [{i}] ID: {painting.Id}, 完整图像: {painting.FullImage}, 缺失部分数量: {painting.MissingPieces?.Count ?? 0}");
            }

            selectedPaintings.Clear();

            // 简化逻辑：只随机选择一个画作
            if (puzzleConfig.Paintings.Count > 0)
            {
                // 随机选择一个画作索引
                int randomIndex = UnityEngine.Random.Range(0, puzzleConfig.Paintings.Count);

                // 添加选中的画作
                selectedPaintings.Add(puzzleConfig.Paintings[randomIndex]);

                Debug.Log($"随机选择了一幅画作: [{randomIndex}] {puzzleConfig.Paintings[randomIndex].Id}");
            }
            else
            {
                Debug.LogError("没有可用的画作，无法选择");
            }

            Debug.Log($"已选择 {selectedPaintings.Count} 幅画作");

            // 检查PuzzlePiecesManager中是否有对应的画作
            if (PuzzlePiecesManager.Instance != null)
            {
                foreach (var painting in selectedPaintings)
                {
                    bool found = false;
                    foreach (var piece in PuzzlePiecesManager.Instance.pieces)
                    {
                        if (piece.id == painting.Id || piece.id == $"{painting.Id}_full")
                        {
                            found = true;
                            Debug.Log($"在PuzzlePiecesManager中找到画作: {painting.Id}，对应的拼图块ID: {piece.id}");
                            break;
                        }
                    }

                    if (!found)
                    {
                        Debug.LogWarning($"在PuzzlePiecesManager中找不到画作: {painting.Id}，请检查ID是否匹配");
                    }
                }
            }
            else
            {
                Debug.LogError("PuzzlePiecesManager实例不存在，无法验证画作是否可用");
            }
        }

        /// <summary>
        /// 游戏开始时调用
        /// </summary>
        protected override void OnGameStarted()
        {
            Debug.Log($"图片拼图游戏开始，当前模板状态: {currentTemplateState}，当前游戏状态: {currentGameplayState}，isGameActive: {isGameActive}");

            try
            {
                // 检查PuzzlePiecesManager是否存在
                if (PuzzlePiecesManager.Instance == null)
                {
                    Debug.LogWarning("PuzzlePiecesManager实例不存在，尝试创建一个新实例");
                    GameObject managerObject = new GameObject("PuzzlePiecesManager");
                    managerObject.AddComponent<PuzzlePiecesManager>();
                    DontDestroyOnLoad(managerObject);
                    Debug.Log("已创建新的PuzzlePiecesManager实例");
                }

                // 清除所有匹配关系，确保从干净的状态开始
                SpriteInteractionHandler.StaticClearAllMatches();
                Debug.Log("已清除所有拼图块匹配关系");
            }
            catch (Exception ex)
            {
                Debug.LogError($"清除拼图块匹配关系时发生异常: {ex.Message}");
                Debug.LogError($"异常堆栈: {ex.StackTrace}");
                // 继续执行，不要因为这个异常而中断游戏流程
            }

            // 不再需要在这里初始化警察表情对象，已经在LoadUIElements中初始化过了

            try
            {
                // 直接显示第一幅画，不再使用协程
                Debug.Log("直接调用ShowCurrentPaintingSync方法");
                ShowCurrentPaintingSync();
            }
            catch (Exception ex)
            {
                Debug.LogError($"显示画作时发生异常: {ex.Message}");
                Debug.LogError($"异常堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 重置游戏状态
        /// </summary>
        protected override void ResetGameState()
        {
            // 重置游戏状态
            gameTime = 0f;
            score = 0;
            hintsUsed = 0;
            correctCount = 0;
            errorCount = 0;
            isGameActive = false;

            // 清理游戏内容
            ClearGameContent();
        }

        /// <summary>
        /// 同步显示当前画作
        /// </summary>
        private void ShowCurrentPaintingSync()
        {
            Debug.Log("【调试】ShowCurrentPaintingSync 开始执行");
            Debug.Log("检查PuzzlePiecesManager初始化状态...");

            // 检查PuzzlePiecesManager是否存在
            if (PuzzlePiecesManager.Instance == null)
            {
                Debug.LogWarning("PuzzlePiecesManager实例不存在，尝试查找...");

                // 尝试查找PuzzlePiecesManager
                PuzzlePiecesManager manager = FindObjectOfType<PuzzlePiecesManager>();
                if (manager != null)
                {
                    Debug.Log($"找到PuzzlePiecesManager实例: {manager.gameObject.name}");
                }
                else
                {
                    Debug.LogError("无法找到PuzzlePiecesManager实例，尝试创建一个新实例");

                    // 创建一个新的PuzzlePiecesManager实例
                    GameObject managerObject = new GameObject("PuzzlePiecesManager");
                    PuzzlePiecesManager newManager = managerObject.AddComponent<PuzzlePiecesManager>();
                    DontDestroyOnLoad(managerObject);

                    Debug.Log("已创建新的PuzzlePiecesManager实例");
                }
            }

            // 再次检查PuzzlePiecesManager是否存在
            if (PuzzlePiecesManager.Instance == null)
            {
                Debug.LogError("PuzzlePiecesManager实例仍然不存在，无法显示画作");
                return;
            }

            // 检查PuzzlePiecesManager中是否有拼图块
            if (PuzzlePiecesManager.Instance.pieces.Count == 0)
            {
                Debug.LogWarning("PuzzlePiecesManager中没有拼图块，可能需要手动添加");

                // 如果没有拼图块，尝试创建测试拼图块
                Debug.Log("尝试手动创建测试拼图块");

                // 反射调用CreateTestPieces方法
                System.Reflection.MethodInfo method = null;
                try
                {
                    method = typeof(PuzzlePiecesManager).GetMethod("CreateTestPieces",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"获取CreateTestPieces方法失败: {ex.Message}");
                }

                if (method != null)
                {
                    try
                    {
                        method.Invoke(PuzzlePiecesManager.Instance, null);
                        Debug.Log("成功调用CreateTestPieces方法创建测试拼图块");
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"调用CreateTestPieces方法失败: {ex.Message}");
                    }
                }
                else
                {
                    Debug.LogError("无法找到CreateTestPieces方法");
                }
            }

            Debug.Log($"PuzzlePiecesManager中拼图块数量: {PuzzlePiecesManager.Instance.pieces.Count}");

            // 确保已选择画作
            if (selectedPaintings.Count == 0)
            {
                Debug.LogWarning("没有选择任何画作，尝试重新选择");
                SelectPaintings();

                // 如果仍然没有选择画作，创建一个默认画作
                if (selectedPaintings.Count == 0)
                {
                    Debug.LogWarning("仍然没有选择任何画作，创建默认画作");

                    // 创建默认画作
                    PaintingData defaultPainting = new PaintingData
                    {
                        Id = "painting_1",
                        FullImage = "painting_1",
                        MissingPieces = new List<PaintingPieceData>()
                    };

                    // 添加缺失部分
                    for (int i = 1; i <= 4; i++)
                    {
                        defaultPainting.MissingPieces.Add(new PaintingPieceData
                        {
                            Id = $"painting_1_piece_{i}",
                            Position = new Vector2(i * 100 - 200, 0)
                        });
                    }

                    selectedPaintings.Add(defaultPainting);
                    Debug.Log("已创建默认画作");
                }
            }

            // 显示第一幅画
            Debug.Log("【调试】准备调用 ShowCurrentPainting");
            ShowCurrentPainting();
            Debug.Log("【调试】ShowCurrentPainting 调用完成");

            // 设置模板状态为Ready，表示游戏已准备好开始
            currentTemplateState = TemplateState.Ready;
            Debug.Log($"模板状态已设置为: {currentTemplateState}，准备开始游戏");
        }

        /// <summary>
        /// 延迟显示当前画作 - 保留以兼容现有代码
        /// </summary>
        private IEnumerator DelayedShowCurrentPainting()
        {
            Debug.Log("DelayedShowCurrentPainting 被调用，但我们使用同步方法代替");
            ShowCurrentPaintingSync();
            yield break;
        }

        /// <summary>
        /// 显示当前画作
        /// </summary>
        private void ShowCurrentPainting()
        {
            Debug.Log("【调试】ShowCurrentPainting 开始执行");

            if (currentPaintingIndex >= selectedPaintings.Count)
            {
                Debug.Log("【调试】currentPaintingIndex >= selectedPaintings.Count，显示空画框或结束游戏");
                // 所有画作都已完成，显示空画框
                if (puzzleConfig.FinalEmptyFrame && puzzleConfig.EmptyFrame != null)
                {
                    ShowEmptyFrame();
                }
                else
                {
                    // 游戏完成
                    EndGame();
                }
                return;
            }

            Debug.Log($"【调试】selectedPaintings.Count = {selectedPaintings.Count}, currentPaintingIndex = {currentPaintingIndex}");

            // 获取当前画作
            PaintingData painting = selectedPaintings[currentPaintingIndex];

            // 检查PuzzlePiecesManager是否存在
            if (PuzzlePiecesManager.Instance == null)
            {
                Debug.LogError("PuzzlePiecesManager实例不存在，请在场景中添加PuzzlePiecesManager组件");
                return;
            }

            Debug.Log($"PuzzlePiecesManager实例存在，拼图块数量: {PuzzlePiecesManager.Instance.pieces.Count}");

            // 检查PuzzlePiecesManager中是否有拼图块
            if (PuzzlePiecesManager.Instance.pieces.Count == 0)
            {
                Debug.LogWarning("PuzzlePiecesManager中没有拼图块，将继续执行但可能无法正确显示画作");
            }

            // 检查是否有对应的拼图块
            bool hasPieces = false;
            try
            {
                foreach (var piece in PuzzlePiecesManager.Instance.pieces)
                {
                    if (piece != null && !string.IsNullOrEmpty(piece.id) && piece.id.StartsWith(painting.Id))
                    {
                        hasPieces = true;
                        Debug.Log($"找到拼图块: {piece.id}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"检查拼图块时发生异常: {ex.Message}");
                Debug.LogError($"异常堆栈: {ex.StackTrace}");
            }

            if (!hasPieces)
            {
                Debug.LogWarning($"PuzzlePiecesManager中没有找到画作 {painting.Id} 的拼图块");
            }

            // 检查并初始化MissingPieces
            if (painting.MissingPieces == null || painting.MissingPieces.Count == 0)
            {
                Debug.Log($"画作 {painting.Id} 在ShowCurrentPainting中没有缺失部分，将根据模板设置生成");

                // 获取模板设置
                int missingPiecesCount = puzzleConfig.TemplateSettings?.MissingPiecesCount ?? 3;
                string positionMode = puzzleConfig.TemplateSettings?.PositionMode ?? "grid";
                // 不再使用radius参数，但保留参数传递以保持方法签名兼容性
                float radius = 0f;

                // 生成缺失部分
                painting.MissingPieces = GenerateMissingPieces(painting.Id, missingPiecesCount, positionMode, radius);

                Debug.Log($"在ShowCurrentPainting中为画作 {painting.Id} 生成了 {painting.MissingPieces.Count} 个缺失部分");
            }

            // 更新PuzzleContainer中的Sprite
            Debug.Log("【调试】准备调用 UpdatePuzzleContainerSprite");
            UpdatePuzzleContainerSprite(painting);
            Debug.Log("【调试】UpdatePuzzleContainerSprite 调用完成");

            // 创建不完整的画作
            Debug.Log("【调试】准备调用 CreateIncompletePainting");
            CreateIncompletePainting(painting);
            Debug.Log("【调试】CreateIncompletePainting 调用完成");

            // 注释掉这行，因为在CreateIncompletePainting中已经创建了拼图块洞
            // CreateMissingPieces(painting);

            // 重置匹配计数
            matchedCount = 0;
            totalPieces = painting.MissingPieces.Count;

            Debug.Log($"显示画作: {painting.Id}, 缺失部分: {totalPieces}");
        }

        /// <summary>
        /// 更新PuzzleContainer中的Sprite
        /// </summary>
        private void UpdatePuzzleContainerSprite(PaintingData painting)
        {
            Debug.Log($"更新PuzzleContainer中的Sprite为当前画作: {painting.Id}");

            // 检查PuzzleContainer是否存在
            if (puzzleContainer == null)
            {
                Debug.LogError("PuzzleContainer为空，无法更新Sprite");
                return;
            }

            // 获取PuzzleContainer上的Image组件
            SpriteRenderer containerImage = puzzleContainer.GetComponent<SpriteRenderer>();
            if (containerImage == null)
            {
                // 如果没有Image组件，尝试在子对象中查找
                containerImage = puzzleContainer.GetComponentInChildren<SpriteRenderer>();

                // 如果仍然没有找到，添加一个Image组件
                if (containerImage == null)
                {
                    Debug.Log("PuzzleContainer上没有找到Image组件，跳过更新Sprite");
                    return;
                }
            }

            // 从PuzzlePiecesManager获取当前画作的完整Sprite
            Sprite fullSprite = null;

            try
            {
                // 检查PuzzlePiecesManager是否存在
                if (PuzzlePiecesManager.Instance == null)
                {
                    Debug.LogError("PuzzlePiecesManager实例不存在，无法获取画作Sprite");
                    return;
                }

                // 尝试直接使用画作ID
                fullSprite = PuzzlePiecesManager.Instance.GetPieceById(painting.Id);

                // 如果找不到，尝试使用带_full后缀的ID
                if (fullSprite == null)
                {
                    string fullId = $"{painting.Id}_full";
                    fullSprite = PuzzlePiecesManager.Instance.GetPieceById(fullId);

                    if (fullSprite != null)
                    {
                        Debug.Log($"使用ID {fullId} 成功获取完整画作Sprite");
                    }
                }
                else
                {
                    Debug.Log($"使用ID {painting.Id} 成功获取完整画作Sprite");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"获取画作Sprite时发生异常: {ex.Message}");
                Debug.LogError($"异常堆栈: {ex.StackTrace}");
            }

            // 如果找到了Sprite，更新Image组件
            if (fullSprite != null)
            {
                containerImage.sprite = fullSprite;
                Debug.Log($"成功更新PuzzleContainer的Sprite为: {painting.Id}");
            }
            else
            {
                Debug.LogWarning($"无法找到画作 {painting.Id} 的Sprite，PuzzleContainer的Sprite未更新");
            }
        }

        /// <summary>
        /// 创建不完整的画作
        /// </summary>
        private void CreateIncompletePainting(PaintingData painting)
        {
            Debug.Log($"【调试】CreateIncompletePainting 开始执行");
            Debug.Log($"创建不完整画作: {painting.IncompleteImage}");

            // 检查puzzleContainer是否存在
            if (puzzleContainer == null)
            {
                Debug.LogError("【调试】puzzleContainer为空，无法创建不完整画作");
                return;
            }

            Debug.Log($"【调试】puzzleContainer存在: {puzzleContainer.name}");

            // 创建一个父容器GameObject
            GameObject containerObject = new GameObject($"IncompletePaintingContainer");
            containerObject.transform.SetParent(puzzleContainer);
            containerObject.transform.localPosition = Vector3.zero;

            // 创建画作GameObject作为子对象
            GameObject paintingObject = new GameObject($"Painting_{painting.Id}");
            paintingObject.transform.SetParent(containerObject.transform);
            paintingObject.transform.localPosition = Vector3.zero;

            // 添加SpriteRenderer组件
            SpriteRenderer spriteRenderer = paintingObject.AddComponent<SpriteRenderer>();

            // 获取puzzleContainer上的SpriteRenderer的sortingOrder
            int baseSortingOrder = 200; // 默认值
            SpriteRenderer containerRenderer = puzzleContainer.GetComponent<SpriteRenderer>();
            if (containerRenderer != null)
            {
                baseSortingOrder = containerRenderer.sortingOrder;
                Debug.Log($"获取到puzzleContainer的sortingOrder: {baseSortingOrder}");
            }
            else
            {
                // 尝试在子对象中查找
                containerRenderer = puzzleContainer.GetComponentInChildren<SpriteRenderer>();
                if (containerRenderer != null)
                {
                    baseSortingOrder = containerRenderer.sortingOrder;
                    Debug.Log($"获取到puzzleContainer子对象的sortingOrder: {baseSortingOrder}");
                }
            }

            // 设置为基础层级+10
            spriteRenderer.sortingOrder = baseSortingOrder + 10;
            Debug.Log($"设置SpriteRenderer的sortingOrder为: {spriteRenderer.sortingOrder} (基础层级{baseSortingOrder} + 10)");

            // 设置适当的缩放以匹配预期大小
            // 注意：这里不再使用RectTransform，而是通过Transform的scale来控制大小
            // 实际项目中可能需要根据Sprite的实际尺寸调整缩放值
            paintingObject.transform.localScale = new Vector3(1f, 1f, 1f);

            // 检查是否需要自动生成不完整图像
            if (puzzleConfig.TemplateSettings != null && puzzleConfig.TemplateSettings.GenerateIncomplete)
            {
                // 直接调用同步方法
                LoadFullImageAndGenerateIncompleteSpriteRenderer(painting, spriteRenderer);
            }

            // 添加到列表 - 注意这里添加的是父容器对象
            paintingObjects.Add(containerObject);

            Debug.Log($"创建了不完整画作容器: {containerObject.name}，包含画作: {paintingObject.name}");
        }

        /// <summary>
        /// 加载完整图像并生成不完整图像 (SpriteRenderer版本)
        /// </summary>
        private void LoadFullImageAndGenerateIncompleteSpriteRenderer(PaintingData painting, SpriteRenderer targetRenderer)
        {
            Debug.Log($"【调试】LoadFullImageAndGenerateIncompleteSpriteRenderer 开始执行，画作ID: {painting.Id}");

            if (string.IsNullOrEmpty(painting.Id))
            {
                Debug.LogError($"画作ID为空");
                return;
            }

            Debug.Log($"【调试】画作ID有效: {painting.Id}");

            // 检查PuzzlePiecesManager是否存在
            if (PuzzlePiecesManager.Instance == null)
            {
                Debug.LogError("【调试】PuzzlePiecesManager实例不存在，无法加载完整画作");
                return;
            }

            Debug.Log($"【调试】PuzzlePiecesManager实例存在，拼图块数量: {PuzzlePiecesManager.Instance.pieces.Count}");

            // 输出所有拼图块ID，方便调试
            Debug.Log("【调试】PuzzlePiecesManager中的拼图块ID列表:");
            foreach (var piece in PuzzlePiecesManager.Instance.pieces)
            {
                Debug.Log($"【调试】拼图块ID: {piece.id}");
            }

            // 加载完整画作
            Sprite fullSprite = null;

            // 直接从PuzzlePiecesManager获取Sprite
            fullSprite = PuzzlePiecesManager.Instance.GetPieceById(painting.Id);

            if (fullSprite == null)
            {
                // 如果直接使用ID找不到，尝试使用带_full后缀的ID
                string fullId = $"{painting.Id}_full";
                fullSprite = PuzzlePiecesManager.Instance.GetPieceById(fullId);

                if (fullSprite == null)
                {
                    Debug.LogError($"找不到完整画作: {painting.Id}，已尝试ID: {painting.Id} 和 {fullId}");
                    return;
                }
                else
                {
                    Debug.Log($"使用ID {fullId} 成功找到完整画作");
                }
            }
            else
            {
                Debug.Log($"使用ID {painting.Id} 成功找到完整画作");
            }

            // 生成不完整图像
            Vector2 blockSize = new Vector2(100, 100); // 默认边框大小
            if (puzzleConfig.TemplateSettings != null && puzzleConfig.TemplateSettings.PieceSize != null)
            {
                // 直接使用配置文件中的pieceSize作为边框大小
                blockSize = puzzleConfig.TemplateSettings.PieceSize;
                Debug.Log($"从配置文件获取边框大小: width={blockSize.x}, height={blockSize.y} 像素");


                Debug.Log($"最终使用的边框大小: width={blockSize.x}, height={blockSize.y} 像素");
            }
            else
            {
                // 如果没有配置，使用默认值
                blockSize = new Vector2(100, 100);
                Debug.Log($"使用默认边框大小: width={blockSize.x}, height={blockSize.y} 像素");
            }

            Sprite incompleteSprite = PuzzleImageProcessor.CalculatePuzzlePiecePositions(fullSprite, painting, blockSize);
            if (incompleteSprite != null)
            {
                incompleteSprite.name = $"Incomplete_{painting.Id}";
            }

            // 设置图像
            if (targetRenderer != null)
            {
                targetRenderer.sprite = incompleteSprite;

                // 根据Sprite的实际尺寸调整GameObject的缩放
                if (incompleteSprite != null)
                {
                    // 计算适当的缩放比例
                    float spriteWidth = incompleteSprite.bounds.size.x;
                    float spriteHeight = incompleteSprite.bounds.size.y;

                    // 保持与父容器相同的缩放比例
                    targetRenderer.transform.localScale = Vector3.one; // localScale是相对于父对象的，设为1表示与父对象大小一致

                    Debug.Log($"设置SpriteRenderer缩放: {targetRenderer.transform.localScale}, Sprite尺寸: {spriteWidth}x{spriteHeight}");
                }
            }

            // 获取targetImage对象（即targetRenderer所在的GameObject）
            GameObject targetImageObject = targetRenderer.gameObject;
            Debug.Log($"获取到targetImage对象: {targetImageObject.name}");

            // 在生成不完整图像后，直接创建拼图块洞
            if (painting.MissingPieces != null && painting.MissingPieces.Count > 0)
            {
                Debug.Log($"在LoadFullImageAndGenerateIncompleteSpriteRenderer中为画作 {painting.Id} 创建 {painting.MissingPieces.Count} 个拼图块洞");

                // 创建字典，用于存储拼图块洞的Transform
                Dictionary<string, Transform> holeTransforms = new Dictionary<string, Transform>();

                // 记录拼图块的原始位置
                foreach (var piece in painting.MissingPieces)
                {
                    Debug.Log($"【坐标跟踪】拼图块 {piece.Id} 的原始位置: {piece.Position}");
                }

                // 我们已经在外部调用了GenerateIncompleteImage，这里不需要再次调用
                // 直接使用已经生成的incompleteSprite

                // 获取Sprite的尺寸
                int spriteWidth = 0;
                int spriteHeight = 0;
                if (incompleteSprite != null)
                {
                    spriteWidth = Mathf.RoundToInt(incompleteSprite.rect.width);
                    spriteHeight = Mathf.RoundToInt(incompleteSprite.rect.height);
                }
                else if (fullSprite != null)
                {
                    spriteWidth = Mathf.RoundToInt(fullSprite.rect.width);
                    spriteHeight = Mathf.RoundToInt(fullSprite.rect.height);
                }

                // 获取像素每单位
                float pixelsPerUnit = fullSprite != null ? fullSprite.pixelsPerUnit : 100f;

                // 使用PuzzleImageProcessor.CreateGridPointsGameObjects方法创建网格点GameObject
                List<Vector2> gridPoints = PuzzleImageProcessor.CreateGridPointsGameObjects(
                    targetImageObject.transform,
                    spriteWidth,
                    spriteHeight,
                    blockSize,
                    pixelsPerUnit,
                    showDebugMarkers // 传递调试开关参数
                );

                Debug.Log($"使用PuzzleImageProcessor.CreateGridPointsGameObjects方法创建了 {gridPoints.Count} 个网格点GameObject");

                // 随机选择网格点索引用于绿色十字点
                List<int> selectedIndices = new List<int>();
                // 确保不会选择超过网格点数量的点
                int pointsToSelect = Mathf.Min(painting.MissingPieces.Count, gridPoints.Count);

                // 创建一个包含所有可能索引的列表
                List<int> allIndices = new List<int>();
                for (int i = 0; i < gridPoints.Count; i++)
                {
                    allIndices.Add(i);
                }

                // 随机打乱索引列表
                for (int i = 0; i < allIndices.Count; i++)
                {
                    int temp = allIndices[i];
                    int randomIndex = UnityEngine.Random.Range(i, allIndices.Count);
                    allIndices[i] = allIndices[randomIndex];
                    allIndices[randomIndex] = temp;
                }

                // 选择前pointsToSelect个索引
                for (int i = 0; i < pointsToSelect; i++)
                {
                    selectedIndices.Add(allIndices[i]);
                    Debug.Log($"随机选择了网格点索引: {allIndices[i]}");
                }

                // 如果当前画作没有绿色点位置数组，创建一个
                if (!greenPointPositions.ContainsKey(painting.Id))
                {
                    greenPointPositions[painting.Id] = new List<Vector2>();
                }

                // 将所有网格点位置添加到绿色点位置数组
                greenPointPositions[painting.Id].AddRange(gridPoints);

                // 创建拼图块洞，使用原始位置
                foreach (var piece in painting.MissingPieces)
                {
                    // 获取拼图块在MissingPieces中的索引
                    int pieceIndex = painting.MissingPieces.IndexOf(piece);

                    // 检查是否有足够的随机选择的索引
                    if (pieceIndex >= selectedIndices.Count)
                    {
                        Debug.LogWarning($"拼图块 {piece.Id} 的索引 {pieceIndex} 超出了随机选择的索引数量 {selectedIndices.Count}，将使用默认索引");
                        pieceIndex = pieceIndex % selectedIndices.Count;
                    }

                    // 使用随机选择的网格点索引
                    int gridIndex = selectedIndices[pieceIndex];
                    Vector2 pointPosition = gridPoints[gridIndex];

                    // 记录拼图块位置信息
                    Debug.Log($"拼图块 {piece.Id} 使用随机选择的网格点索引: {gridIndex}, 位置: {pointPosition}");

                    // 查找对应的红色网格点GameObject
                    // 计算行列索引 - 这里需要根据实际的网格布局计算
                    // 假设网格是按行优先排列的，每行有sqrt(gridPoints.Count)个点
                    int cols = Mathf.CeilToInt(Mathf.Sqrt(gridPoints.Count));
                    int row = gridIndex / cols;
                    int col = gridIndex % cols;
                    string gridPointName = $"GridPoint_Row{row}_Col{col}";
                    Transform gridPointsContainer = targetImageObject.transform.Find("GridPointsContainer");
                    Transform redGridPoint = null;

                    if (gridPointsContainer != null)
                    {
                        redGridPoint = gridPointsContainer.Find(gridPointName);

                        // 如果找不到精确名称的网格点，尝试查找所有网格点并找到最接近的一个
                        if (redGridPoint == null)
                        {
                            Debug.LogWarning($"找不到名为 {gridPointName} 的红色网格点，尝试查找最接近的网格点");

                            // 获取所有网格点
                            Transform[] allGridPoints = new Transform[gridPointsContainer.childCount];
                            for (int i = 0; i < gridPointsContainer.childCount; i++)
                            {
                                allGridPoints[i] = gridPointsContainer.GetChild(i);
                            }

                            // 找到距离最近的网格点
                            float minDistance = float.MaxValue;
                            foreach (Transform gridPoint in allGridPoints)
                            {
                                float distance = Vector2.Distance(gridPoint.localPosition, new Vector2(pointPosition.x, pointPosition.y));
                                if (distance < minDistance)
                                {
                                    minDistance = distance;
                                    redGridPoint = gridPoint;
                                }
                            }

                            if (redGridPoint != null)
                            {
                                Debug.Log($"找到最接近的网格点: {redGridPoint.name}，距离: {minDistance}");
                            }
                        }
                    }

                    // 创建绿色点GameObject - 选择拼图块后显示的十字标记
                    GameObject greenPointObject = new GameObject($"GreenPoint_{piece.Id}");

                    // 如果找到了红色网格点，使用它的父对象和位置
                    if (redGridPoint != null)
                    {
                        greenPointObject.transform.SetParent(redGridPoint.parent);
                        greenPointObject.transform.localPosition = redGridPoint.localPosition;
                        Debug.Log($"【坐标跟踪】绿色十字点 {piece.Id} 使用红色网格点 {redGridPoint.name} 的位置: {greenPointObject.transform.localPosition}");
                    }
                    else
                    {
                        // 如果找不到红色网格点，使用原来的方法
                        greenPointObject.transform.SetParent(targetImageObject.transform);
                        greenPointObject.transform.localPosition = new Vector3(pointPosition.x, pointPosition.y, 0);
                        Debug.Log($"【坐标跟踪】绿色十字点 {piece.Id} 使用计算的位置: {greenPointObject.transform.localPosition}，世界坐标: {greenPointObject.transform.position}");
                    }

                    // 在创建绿色十字点的同时，提取并保存拼图块的纹理
                    if (fullSprite != null)
                    {
                        // 获取配置文件中的拼图块大小
                        Vector2 pieceSize = new Vector2(100, 100); // 默认拼图块大小
                        if (puzzleConfig.TemplateSettings != null && puzzleConfig.TemplateSettings.PieceSize != null)
                        {
                            pieceSize = puzzleConfig.TemplateSettings.PieceSize;
                            Debug.Log($"从配置文件获取拼图块大小: {pieceSize.x}x{pieceSize.y}");
                        }

                        // 提取拼图块纹理
                        ExtractPieceTexture(fullSprite, piece, pieceSize, greenPointObject.transform.position);

                        if (piece.PieceSprite != null)
                        {
                            Debug.Log($"成功为拼图块 {piece.Id} 提取并保存纹理");
                        }
                        else
                        {
                            Debug.LogWarning($"无法为拼图块 {piece.Id} 提取纹理");
                        }
                    }

                    // 添加SpriteRenderer组件
                    SpriteRenderer pointRenderer = greenPointObject.AddComponent<SpriteRenderer>();

                    // 创建一个小的绿色十字形Sprite
                    Texture2D pointTexture = new Texture2D(11, 11);
                    Color[] pixels = new Color[11 * 11];
                    for (int i = 0; i < pixels.Length; i++)
                    {
                        // 默认设置为透明
                        pixels[i] = Color.clear;
                    }

                    // 绘制十字形 - 水平线
                    for (int x = 0; x < 11; x++)
                    {
                        int y = 5; // 中间行
                        pixels[y * 11 + x] = Color.green;
                    }

                    // 绘制十字形 - 垂直线
                    for (int y = 0; y < 11; y++)
                    {
                        int x = 5; // 中间列
                        pixels[y * 11 + x] = Color.green;
                    }

                    // 绘制十字形的中心点，使其更明显
                    for (int y = 4; y <= 6; y++)
                    {
                        for (int x = 4; x <= 6; x++)
                        {
                            pixels[y * 11 + x] = Color.green;
                        }
                    }

                    // 应用像素
                    pointTexture.SetPixels32(Array.ConvertAll(pixels, color => (Color32)color));
                    pointTexture.Apply();

                    // 创建Sprite
                    Sprite pointSprite = Sprite.Create(
                        pointTexture,
                        new Rect(0, 0, 11, 11),
                        new Vector2(0.5f, 0.5f),
                        100f
                    );

                    // 设置SpriteRenderer
                    pointRenderer.sprite = pointSprite;
                    // 获取红色点的sortingOrder并加10，确保绿色点显示在红色点上层
                    int redPointSortingOrder = 140; // 红色点的默认sortingOrder
                    pointRenderer.sortingOrder = redPointSortingOrder + 10;

                    // 根据调试开关决定是否显示绿色十字点
                    pointRenderer.enabled = showDebugMarkers;

                    Debug.Log($"为拼图块 {piece.Id} 创建了绿色十字标记，位置: {greenPointObject.transform.localPosition}，显示状态: {pointRenderer.enabled}");

                    // 直接在创建绿色十字点后创建对应的洞
                    GameObject holeObject = Instantiate(puzzleHolePrefab, gridPointsContainer);
                    holeObject.name = $"Hole_{piece.Id}";

                    // 将洞放在与绿色十字点相同的位置
                    if (redGridPoint != null)
                    {
                        holeObject.transform.position = redGridPoint.position;
                    }
                    else
                    {
                        holeObject.transform.position = greenPointObject.transform.position;
                    }

                    // 设置holeObject的图层为绿色点的图层加10
                    // 获取绿色点的sortingOrder
                    int greenPointSortingOrder = pointRenderer.sortingOrder;
                    // 使用SpriteUtils工具类设置holeObject的sortingOrder为绿色点的sortingOrder加10
                    SpriteUtils.SetSpriteSortingOrder(holeObject, greenPointSortingOrder + 10);
                    Debug.Log($"设置洞 {piece.Id} 的sortingOrder为 {greenPointSortingOrder + 10}，绿色点的sortingOrder为 {greenPointSortingOrder}");

                    // 根据配置文件中的pieceSize设置洞的大小
                    Vector2 holePieceSize = new(100, 100); // 默认拼图块大小
                    if (puzzleConfig.TemplateSettings != null && puzzleConfig.TemplateSettings.PieceSize != null)
                    {
                        holePieceSize = puzzleConfig.TemplateSettings.PieceSize;
                        Debug.Log($"从配置文件获取拼图块大小: {holePieceSize.x}x{holePieceSize.y}");
                    }

                    // 获取洞的SpriteRenderer组件
                    if (!holeObject.TryGetComponent(out SpriteRenderer holeRenderer))
                    {
                        holeRenderer = holeObject.AddComponent<SpriteRenderer>();
                    }

                    // 检查拼图块是否有PieceSprite
                    if (piece.PieceSprite != null)
                    {
                        // 使用拼图块的精灵来设置洞的精灵
                        Debug.Log($"使用拼图块 {piece.Id} 的精灵来设置洞的精灵");
                        holeRenderer.sprite = piece.PieceSprite;

                        // 设置大小为1，使用精灵的实际大小
                        holeObject.transform.localScale = Vector3.one;
                        Debug.Log($"设置洞 {piece.Id} 的大小: 使用拼图块精灵的实际大小，缩放比例=1");
                    }
                    else
                    {
                        // 如果拼图块没有精灵，则使用单像素纹理作为备选方案
                        Debug.LogWarning($"拼图块 {piece.Id} 没有精灵，使用单像素纹理作为备选方案");

                        // 创建白色的单像素精灵
                        Color holeColor = Color.white;

                        // 创建单像素纹理
                        Texture2D holeTexture = new(1, 1, TextureFormat.RGBA32, false);
                        holeTexture.SetPixel(0, 0, holeColor);
                        holeTexture.Apply();

                        // 创建Sprite - 使用pixelsPerUnit=100，保持Unity标准比例
                        Sprite holeSprite = Sprite.Create(
                            holeTexture,
                            new Rect(0, 0, 1, 1),
                            new Vector2(0.5f, 0.5f), // 中心点
                            100f // 设置pixelsPerUnit为100，保持Unity标准比例
                        );
                        holeSprite.name = $"HoleSprite_{piece.Id}";

                        // 设置精灵
                        holeRenderer.sprite = holeSprite;

                        // 设置大小（通过缩放）
                        // 使用配置文件中的长宽值直接作为缩放比例
                        holeObject.transform.localScale = new Vector3(holePieceSize.x, holePieceSize.y, 1.0f);
                        Debug.Log($"设置洞 {piece.Id} 的大小: 纹理大小=1x1像素, pixelsPerUnit=100, 缩放比例={holePieceSize.x}x{holePieceSize.y}");
                    }

                    // 设置为纯白色，透明度为5%不透明度（95%透明度）
                    if (holeRenderer != null)
                    {
                        SpriteUtils.ChangeSpriteAlphaColor(holeObject, 0.95f);
                    }

                    // 设置碰撞器
                    if (!holeObject.TryGetComponent(out BoxCollider2D collider))
                    {
                        collider = holeObject.AddComponent<BoxCollider2D>();
                    }

                    // 设置碰撞器为触发器
                    collider.isTrigger = true;

                    // 设置碰撞器大小
                    if (holeRenderer.sprite != null)
                    {
                        // 使用SpriteUtils设置碰撞器，传入pixelsPerUnit=100，碰撞器缩放比例为0.5，使拼图块需要与洞有至少50%的重合面积才能匹配成功
                        SpriteUtils.SetupBoxCollider2D(holeObject, holeRenderer.sprite, 100f, Vector2.zero, 0.5f);
                        Debug.Log($"为洞 {piece.Id} 设置了与Sprite匹配的碰撞器，pixelsPerUnit=100，碰撞器缩放比例=0.5");
                    }
                    else
                    {
                        // 如果没有Sprite，使用默认大小
                        collider.size = new Vector2(1f, 1f);
                        Debug.Log($"为洞 {piece.Id} 设置了默认大小的碰撞器");
                    }

                    // 添加到字典
                    holeTransforms.Add(piece.Id, holeObject.transform);

                    Debug.Log($"直接创建拼图块洞: {holeObject.name}，对应piece ID: {piece.Id}，位置: {holeObject.transform.position}");
                }

                // 创建拼图块并随机排列
                if (holeTransforms != null && holeTransforms.Count > 0)
                {
                    CreatePiecesWithHoles(painting, holeTransforms);
                    ShufflePiecesSpriteRenderer();
                }
                else
                {
                    Debug.LogWarning("没有创建拼图块洞，无法创建拼图块");
                }
            }
        }





        /// <summary>
        /// 移除文件扩展名
        /// </summary>
        private string RemoveExtension(string path)
        {
            int lastDotIndex = path.LastIndexOf('.');
            if (lastDotIndex > 0)
            {
                return path[..lastDotIndex]; // 使用范围运算符替代Substring
            }
            return path;
        }

        /// <summary>
        /// 提取拼图块纹理
        /// </summary>
        /// <param name="fullSprite">完整图像</param>
        /// <param name="piece">拼图块数据</param>
        /// <param name="pieceSize">拼图块大小</param>
        /// <param name="greenPointPosition">绿色十字点位置</param>
        private void ExtractPieceTexture(Sprite fullSprite, PaintingPieceData piece, Vector2 pieceSize, Vector3 greenPointPosition)
        {
            try
            {
                Debug.Log($"开始为拼图块 {piece.Id} 提取纹理，绿色十字点位置: {greenPointPosition}");

                // 获取完整图像的纹理
                Texture2D fullTexture = fullSprite.texture;

                // 获取Sprite在纹理中的区域
                Rect spriteRect = fullSprite.rect;
                int spriteX = Mathf.RoundToInt(spriteRect.x);
                int spriteY = Mathf.RoundToInt(spriteRect.y);
                int spriteWidth = Mathf.RoundToInt(spriteRect.width);
                int spriteHeight = Mathf.RoundToInt(spriteRect.height);

                Debug.Log($"完整图像区域: 左上角=({spriteX}, {spriteY}), 大小={spriteWidth}x{spriteHeight}");

                // 计算拼图块的像素大小
                int piecePixelWidth = Mathf.RoundToInt(pieceSize.x);
                int piecePixelHeight = Mathf.RoundToInt(pieceSize.y);

                Debug.Log($"拼图块像素大小: {piecePixelWidth}x{piecePixelHeight}");

                // 确保拼图块大小不超过图像大小
                piecePixelWidth = Mathf.Min(piecePixelWidth, spriteWidth);
                piecePixelHeight = Mathf.Min(piecePixelHeight, spriteHeight);

                // 计算拼图块在原始图像中的位置
                // 将绿色十字点的世界坐标转换为图像中的像素坐标

                // 获取绿色十字点在图像中的相对位置（0-1范围）
                Vector3 localPos = puzzleContainer.InverseTransformPoint(greenPointPosition);

                // 将相对位置转换为像素坐标
                float pixelsPerUnit = fullSprite.pixelsPerUnit;
                int centerX = spriteX + Mathf.RoundToInt((localPos.x + spriteWidth / (2 * pixelsPerUnit)) * pixelsPerUnit);
                int centerY = spriteY + Mathf.RoundToInt((localPos.y + spriteHeight / (2 * pixelsPerUnit)) * pixelsPerUnit);

                // 计算拼图块的左上角坐标
                int pieceX = centerX - piecePixelWidth / 2;
                int pieceY = centerY - piecePixelHeight / 2;

                // 确保不超出图像边界
                pieceX = Mathf.Clamp(pieceX, spriteX, spriteX + spriteWidth - piecePixelWidth);
                pieceY = Mathf.Clamp(pieceY, spriteY, spriteY + spriteHeight - piecePixelHeight);

                Debug.Log($"拼图块在完整图像中的像素区域: 左上角=({pieceX}, {pieceY}), 大小={piecePixelWidth}x{piecePixelHeight}");

                // 创建一个可读写的纹理副本
                Texture2D readableTexture = new(fullTexture.width, fullTexture.height, TextureFormat.RGBA32, false);
                RenderTexture renderTexture = RenderTexture.GetTemporary(fullTexture.width, fullTexture.height, 0, RenderTextureFormat.Default, RenderTextureReadWrite.Linear);
                Graphics.Blit(fullTexture, renderTexture);
                RenderTexture previous = RenderTexture.active;
                RenderTexture.active = renderTexture;
                readableTexture.ReadPixels(new Rect(0, 0, fullTexture.width, fullTexture.height), 0, 0);
                readableTexture.Apply();
                RenderTexture.active = previous;
                RenderTexture.ReleaseTemporary(renderTexture);

                // 创建拼图块纹理
                Texture2D pieceTexture = new(piecePixelWidth, piecePixelHeight, TextureFormat.RGBA32, false);

                // 从完整图像中提取拼图块区域的像素
                Color[] piecePixels = readableTexture.GetPixels(pieceX, pieceY, piecePixelWidth, piecePixelHeight);

                // 设置拼图块纹理的像素 - 使用SetPixels32以提高性能
                Color32[] piecePixels32 = Array.ConvertAll(piecePixels, color => (Color32)color);
                pieceTexture.SetPixels32(piecePixels32);
                pieceTexture.Apply();

                // 创建拼图块精灵
                Sprite pieceSprite = Sprite.Create(
                    pieceTexture,
                    new Rect(0, 0, pieceTexture.width, pieceTexture.height),
                    new Vector2(0.5f, 0.5f), // 中心点
                    pixelsPerUnit
                );
                pieceSprite.name = $"Piece_{piece.Id}";

                // 设置PaintingPieceData的PieceSprite属性
                piece.PieceSprite = pieceSprite;

                Debug.Log($"成功为拼图块 {piece.Id} 提取纹理，大小: {pieceTexture.width}x{pieceTexture.height}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"提取拼图块纹理时发生错误: {ex.Message}\n{ex.StackTrace}");
            }
        }





        /// <summary>
        /// 使用洞的位置信息创建拼图块
        /// </summary>
        private void CreatePiecesWithHoles(PaintingData painting, Dictionary<string, Transform> holeTransforms)
        {
            // 获取基础层级
            int baseSortingOrder = 0;
            if (puzzleContainer.TryGetComponent(out SpriteRenderer containerRenderer))
            {
                baseSortingOrder = containerRenderer.sortingOrder;
                Debug.Log($"获取到puzzleContainer的sortingOrder: {baseSortingOrder}");
            }
            else
            {
                // 尝试在子对象中查找
                containerRenderer = puzzleContainer.GetComponentInChildren<SpriteRenderer>();
                if (containerRenderer != null)
                {
                    baseSortingOrder = containerRenderer.sortingOrder;
                    Debug.Log($"获取到puzzleContainer子对象的sortingOrder: {baseSortingOrder}");
                }
            }

            // 加载完整画作
            Sprite fullSprite = null;
            if (PuzzlePiecesManager.Instance != null)
            {
                fullSprite = PuzzlePiecesManager.Instance.GetPieceById(painting.Id);
                if (fullSprite == null)
                {
                    // 如果直接使用ID找不到，尝试使用带_full后缀的ID
                    string fullId = $"{painting.Id}_full";
                    fullSprite = PuzzlePiecesManager.Instance.GetPieceById(fullId);
                    if (fullSprite == null)
                    {
                        Debug.LogError($"找不到完整画作: {painting.Id}，已尝试ID: {painting.Id} 和 {fullId}");
                        return;
                    }
                    else
                    {
                        Debug.Log($"使用ID {fullId} 成功找到完整画作");
                    }
                }
                else
                {
                    Debug.Log($"使用ID {painting.Id} 成功找到完整画作");
                }
            }
            else
            {
                Debug.LogError("PuzzlePiecesManager实例不存在，无法加载完整画作");
                return;
            }

            // 获取配置文件中的拼图块大小
            Vector2 pieceSize = new(100, 100); // 默认拼图块大小
            if (puzzleConfig.TemplateSettings != null && puzzleConfig.TemplateSettings.PieceSize != null)
            {
                pieceSize = puzzleConfig.TemplateSettings.PieceSize;
                Debug.Log($"从配置文件获取拼图块大小: {pieceSize.x}x{pieceSize.y}");
            }

            // 创建可拖拽的拼图块
            foreach (var piece in painting.MissingPieces)
            {
                // 检查是否有对应的洞
                if (!holeTransforms.TryGetValue(piece.Id, out Transform holeTransform))
                {
                    Debug.LogWarning($"找不到拼图块 {piece.Id} 对应的洞");
                    continue;
                }

                // 创建拼图块
                GameObject pieceObject = Instantiate(puzzlePiecePrefab, piecesContainer);
                pieceObject.name = $"Piece_{piece.Id}";

                // 设置位置和大小
                // 注意：这里不再使用RectTransform，而是使用Transform

                // 获取洞的世界坐标
                Vector3 holeWorldPosition = holeTransform.position;

                // 记录洞的世界坐标和本地坐标，用于调试
                Debug.Log($"【坐标跟踪】洞 {piece.Id} 的世界坐标: {holeWorldPosition}，本地坐标: {holeTransform.localPosition}");

                // 将洞的世界坐标转换为拼图块容器的局部坐标
                Vector3 pieceLocalPosition = piecesContainer.InverseTransformPoint(holeWorldPosition);

                // 保存原始位置，用于后续参考
                Vector3 originalPosition = new(piece.Position.x, piece.Position.y, 0);

                // 输出坐标转换前后的差异，用于调试
                Debug.Log($"【坐标跟踪】拼图块 {piece.Id} 的原始位置: {originalPosition}，转换后位置: {pieceLocalPosition}，差异: {pieceLocalPosition - originalPosition}");

                // 使用转换后的位置设置拼图块的初始位置
                // 注意：这个位置会在ShufflePieces中被随机排列
                pieceObject.transform.localPosition = pieceLocalPosition;

                // 设置大小为1
                pieceObject.transform.localScale = new Vector3(1f, 1f, 1f);

                // 记录最终设置的位置信息
                Debug.Log($"【坐标跟踪】拼图块 {piece.Id} 设置初始位置: {pieceObject.transform.localPosition}，世界坐标: {pieceObject.transform.position}");

                // 获取或添加SpriteRenderer组件
                if (!pieceObject.TryGetComponent(out SpriteRenderer pieceRenderer))
                {
                    pieceRenderer = pieceObject.AddComponent<SpriteRenderer>();
                }

                // 设置SpriteRenderer的层级
                pieceRenderer.sortingOrder = baseSortingOrder + 20; // 确保拼图块显示在最上层

                // 获取交互处理组件
                if (!pieceObject.TryGetComponent(out SpriteInteractionHandler interactionHandler))
                {
                    interactionHandler = pieceObject.AddComponent<SpriteInteractionHandler>();
                }

                // 初始化交互处理组件
                if (holeTransform != null)
                {
                    // 检查洞的名称是否以"Hole_"开头
                    string targetId = holeTransform.name;
                    if (targetId.StartsWith("Hole_"))
                    {
                        // 如果洞的名称以"Hole_"开头，则使用去掉"Hole_"前缀的名称作为目标ID
                        targetId = targetId["Hole_".Length..];
                        Debug.Log($"洞的名称以Hole_开头，提取目标ID: {targetId}");
                    }
                    else
                    {
                        Debug.Log($"洞的名称不以Hole_开头，直接使用名称作为目标ID: {targetId}");
                    }

                    // 注册匹配关系
                    SpriteInteractionHandler.StaticRegisterMatch(piece.Id, targetId);
                    Debug.Log($"注册匹配关系: 拼图块ID={piece.Id}, 目标ID={targetId}");

                    // 使用拼图块ID作为拼图块ID，使用洞的名称（可能去掉了"Hole_"前缀）作为目标ID
                    interactionHandler.InitializeInteraction(piece.Id, holeTransform, targetId);
                    Debug.Log($"初始化拼图块交互处理组件: 拼图块ID={piece.Id}, 目标ID={targetId}, 洞名称={holeTransform.name}");
                }
                else
                {
                    Debug.LogError($"拼图块 {piece.Id} 对应的洞Transform为null，无法初始化交互处理组件");
                }

                // 添加事件监听
                interactionHandler.OnPieceMatchedEvent += HandlePieceMatched;

                // 启用调试模式
                interactionHandler.enableDebugMode = true;
                Debug.Log($"为拼图块 {piece.Id} 的SpriteInteractionHandler启用了调试模式");

                // 设置图像
                if (pieceRenderer != null)
                {
                    // 优先使用在创建绿色十字点时保存的拼图块纹理
                    if (piece.PieceSprite != null)
                    {
                        Debug.Log($"使用已保存的拼图块纹理: {piece.Id}");
                        pieceRenderer.sprite = piece.PieceSprite;

                        // 使用SpriteUtils设置碰撞器
                        SpriteUtils.SetupBoxCollider2D(pieceObject, piece.PieceSprite, 100f, Vector2.zero, 1.0f);
                        Debug.Log($"为拼图块 {piece.Id} 设置了与Sprite匹配的碰撞器");
                    }
                    // 如果没有保存的纹理，但有完整图像，则提取纹理
                    else if (fullSprite != null)
                    {
                        Debug.Log($"拼图块 {piece.Id} 没有保存的纹理，将从完整图像中提取");

                        // 获取绿色十字点在画布中的位置
                        Vector3 greenPointWorldPos = holeTransform.position;

                        // 提取拼图块纹理
                        ExtractPieceTexture(fullSprite, piece, pieceSize, greenPointWorldPos);

                        if (piece.PieceSprite != null)
                        {
                            // 设置拼图块精灵
                            pieceRenderer.sprite = piece.PieceSprite;

                            // 使用SpriteUtils设置碰撞器
                            SpriteUtils.SetupBoxCollider2D(pieceObject, piece.PieceSprite, 100f, Vector2.zero, 1.0f);
                            Debug.Log($"为拼图块 {piece.Id} 设置了与Sprite匹配的碰撞器");
                        }
                        else
                        {
                            Debug.LogError($"无法为拼图块 {piece.Id} 提取纹理");
                        }
                    }
                    // 如果没有保存的纹理也没有完整图像，但配置了自动生成，则使用原来的方法
                    else if (puzzleConfig.TemplateSettings != null && puzzleConfig.TemplateSettings.GenerateIncomplete)
                    {
                        Debug.Log($"拼图块 {piece.Id} 没有保存的纹理也没有完整图像，将使用自动生成方法");
                        LoadFullImageAndGeneratePieceSpriteRenderer(painting, piece, pieceRenderer);
                    }
                    else
                    {
                        Debug.LogError($"无法为拼图块 {piece.Id} 设置纹理：没有保存的纹理、完整图像或自动生成配置");
                    }
                }

                // 添加到列表
                pieceObjects.Add(pieceObject);
            }

            // 随机排列缺失部分
            ShufflePiecesSpriteRenderer();
        }













        /// <summary>
        /// 随机排列缺失部分 - SpriteRenderer版本
        /// </summary>
        private void ShufflePiecesSpriteRenderer()
        {
            Debug.Log("随机排列缺失部分 (SpriteRenderer模式)");

            // 从配置文件中获取拼图块大小
            float defaultWidth = 1f;
            float defaultHeight = 1f;

            // 如果配置文件中有pieceSize，则使用配置文件中的值
            if (puzzleConfig.TemplateSettings != null && puzzleConfig.TemplateSettings.PieceSize != null)
            {
                // 不再除以100，直接使用配置文件中的值
                defaultWidth = puzzleConfig.TemplateSettings.PieceSize.x;
                defaultHeight = puzzleConfig.TemplateSettings.PieceSize.y;
                Debug.Log($"从配置文件获取拼图块大小: {defaultWidth}x{defaultHeight} Unity单位");
            }
            else
            {
                Debug.LogWarning("配置文件中没有找到pieceSize，使用默认值: 1x1 Unity单位");
            }

            float containerWidth, containerHeight;

            // 获取容器的边界
            if (!piecesContainer.TryGetComponent(out Renderer containerRenderer))
            {
                // 如果没有Renderer组件，使用默认大小
                Debug.LogWarning("拼图块容器没有Renderer组件，使用默认大小");

                // 使用默认大小
                containerWidth = defaultWidth;
                containerHeight = defaultHeight;
            }
            else
            {
                // 获取容器的边界
                Bounds containerBounds = containerRenderer.bounds;
                containerWidth = containerBounds.size.x;
                containerHeight = containerBounds.size.y;
            }

            // 计算可用区域（留出边距）
            float marginX = containerWidth * 0.1f;
            float marginY = containerHeight * 0.1f;
            float availableWidth = containerWidth - marginX * 2;
            float availableHeight = containerHeight - marginY * 2;

            // 计算每个拼图块的平均大小
            float avgPieceSize = Mathf.Sqrt(availableWidth * availableHeight / pieceObjects.Count);

            // 计算网格布局 - 只需要计算列数，因为我们不再使用行来定位
            int cols = Mathf.Max(1, Mathf.FloorToInt(availableWidth / avgPieceSize));

            // 计算单元格宽度 - 只需要水平方向的单元格大小
            float cellWidth = availableWidth / cols;

            // 随机排列拼图块
            List<int> indices = new();
            // 计算需要多少个索引位置 - 至少要有pieceObjects.Count个
            int totalPositions = Mathf.Max(pieceObjects.Count, cols);
            for (int i = 0; i < totalPositions; i++)
            {
                indices.Add(i);
            }

            // 打乱索引
            for (int i = 0; i < indices.Count; i++)
            {
                int temp = indices[i];
                int randomIndex = UnityEngine.Random.Range(i, indices.Count);
                indices[i] = indices[randomIndex];
                indices[randomIndex] = temp;
            }

            // 设置拼图块位置
            for (int i = 0; i < pieceObjects.Count; i++)
            {
                int index = indices[i];
                // 只需要计算列索引，因为我们只关心水平位置
                int col = index % cols;

                // 计算水平位置 - 保持原来的计算方法
                float x = -availableWidth / 2 + col * cellWidth + cellWidth / 2 + UnityEngine.Random.Range(-cellWidth * 0.1f, cellWidth * 0.1f);

                // 垂直位置直接使用0（PiecesContainer的中心位置）
                // 这样拼图块就会在PiecesContainer的垂直中心位置
                float y = 0f;

                // 设置位置
                Vector3 newPosition = new(x, y, 0);
                pieceObjects[i].transform.localPosition = newPosition;
                //pieceObjects[i].transform.position = new Vector3(pieceObjects[i].transform.position.x,
                //piecesContainer.transform.position.x,
                //pieceObjects[i].transform.position.z
                //);

                // 输出调试信息
                Debug.Log($"【随机排列】拼图块 {i} 设置随机位置: {newPosition}，世界坐标: {pieceObjects[i].transform.position}");
            }
        }

        /// <summary>
        /// 加载完整图像并生成拼图块 (SpriteRenderer版本)
        /// </summary>
        private void LoadFullImageAndGeneratePieceSpriteRenderer(PaintingData painting, PaintingPieceData piece, SpriteRenderer targetRenderer)
        {
            if (string.IsNullOrEmpty(painting.Id))
            {
                Debug.LogError($"画作ID为空");
                return;
            }

            // 检查PuzzlePiecesManager是否存在
            if (PuzzlePiecesManager.Instance == null)
            {
                Debug.LogError("PuzzlePiecesManager实例不存在，无法加载完整画作");
                return;
            }

            // 直接从PuzzlePiecesManager获取Sprite
            Sprite fullSprite = PuzzlePiecesManager.Instance.GetPieceById(painting.Id);

            if (fullSprite == null)
            {
                // 如果直接使用ID找不到，尝试使用带_full后缀的ID
                string fullId = $"{painting.Id}_full";
                fullSprite = PuzzlePiecesManager.Instance.GetPieceById(fullId);

                if (fullSprite == null)
                {
                    Debug.LogError($"找不到完整画作: {painting.Id}，已尝试ID: {painting.Id} 和 {fullId}");
                    return;
                }
                else
                {
                    Debug.Log($"使用ID {fullId} 成功找到完整画作");
                }
            }
            else
            {
                Debug.Log($"使用ID {painting.Id} 成功找到完整画作");
            }

            // 生成拼图块图像
            Vector2 pieceSize = new(100, 100); // 默认拼图块大小
            if (puzzleConfig.TemplateSettings != null && puzzleConfig.TemplateSettings.PieceSize != null)
            {
                pieceSize = puzzleConfig.TemplateSettings.PieceSize;
                Debug.Log($"从配置文件获取拼图块大小: {pieceSize.x}x{pieceSize.y}");
            }

            Sprite pieceSprite = PuzzleImageProcessor.GeneratePieceImage(fullSprite, piece, pieceSize);

            // 设置图像 - 优先使用PieceSprite属性
            if (targetRenderer != null)
            {
                // 检查是否已经设置了PieceSprite属性
                if (piece.PieceSprite != null)
                {
                    Debug.Log($"使用拼图块 {piece.Id} 的PieceSprite属性");
                    targetRenderer.sprite = piece.PieceSprite;
                }
                else
                {
                    Debug.Log($"拼图块 {piece.Id} 的PieceSprite属性为空，使用生成的pieceSprite");
                    targetRenderer.sprite = pieceSprite;
                    // 同时设置PieceSprite属性，以便后续使用
                    piece.PieceSprite = pieceSprite;
                }

                // 设置碰撞器
                GameObject targetObject = targetRenderer.gameObject;
                SpriteUtils.SetupBoxCollider2D(targetObject, targetRenderer.sprite, 100f, Vector2.zero, 1.0f);
                Debug.Log($"为拼图块 {piece.Id} 设置了与Sprite匹配的碰撞器");

                // 根据Sprite的实际尺寸调整GameObject的缩放
                Sprite usedSprite = targetRenderer.sprite;
                if (usedSprite != null)
                {
                    // 计算适当的缩放比例
                    float spriteWidth = usedSprite.bounds.size.x;
                    float spriteHeight = usedSprite.bounds.size.y;

                    // 设置合适的缩放比例，确保拼图块在屏幕上有合适的大小
                    // 由于拼图块可能太小，我们需要设置一个较大的缩放比例

                    // 输出原始bounds信息，以便调试
                    Debug.Log($"原始精灵bounds: width={spriteWidth}, height={spriteHeight}");

                    // 不再根据配置文件计算缩放比例，直接设置为1
                    targetRenderer.transform.localScale = new Vector3(1f, 1f, 1f);
                    Debug.Log($"设置拼图块缩放比例为1，不再根据配置文件计算");

                    // 输出调试信息
                    if (puzzleConfig.TemplateSettings != null && puzzleConfig.TemplateSettings.PieceSize != null)
                    {
                        float configPieceWidth = puzzleConfig.TemplateSettings.PieceSize.x;
                        float configPieceHeight = puzzleConfig.TemplateSettings.PieceSize.y;
                        Debug.Log($"配置文件中的拼图块大小: {configPieceWidth}x{configPieceHeight}，但不再用于计算缩放比例");
                    }

                    Debug.Log($"设置SpriteRenderer缩放: {targetRenderer.transform.localScale}, Sprite尺寸: {spriteWidth}x{spriteHeight}");
                }
            }
        }

        /// <summary>
        /// 显示空画框
        /// </summary>
        private void ShowEmptyFrame()
        {
            Debug.Log("显示空画框");

            // 清理当前画作
            ClearCurrentPainting();

            // 在实际项目中，这里应该创建空画框GameObject
            // 例如：加载空画框图像，创建GameObject，设置位置等

            // 播放音效
            if (audioSource != null && missingPaintingSound != null)
            {
                audioSource.PlayOneShot(missingPaintingSound);
            }

            // 显示提示信息
            if (puzzleConfig.EmptyFrame != null && puzzleConfig.EmptyFrame.Message != null)
            {
                // 在实际项目中，这里应该显示提示信息
                // 例如：创建文本UI，设置文本内容等
                Debug.Log($"空画框提示信息: {GetLocalizedMessage(puzzleConfig.EmptyFrame.Message)}");
            }

            // 触发空画框显示事件
            showingEmptyFrame = true;
            EmptyFrameShown?.Invoke();

            // 延迟结束游戏
            StartCoroutine(DelayedEndGameForSinglePainting(3f));
        }



        /// <summary>
        /// 处理拼图块匹配
        /// </summary>
        /// <param name="pieceId">拼图块ID</param>
        /// <param name="targetId">目标位置ID</param>
        /// <param name="isMatch">是否匹配成功</param>
        public void HandlePieceMatched(string pieceId, string targetId, bool isMatch)
        {
            Debug.Log($"拼图块匹配: 块ID={pieceId}, 目标ID={targetId}, 匹配结果={isMatch}");

            if (isMatch)
            {
                // 匹配成功
                matchedCount++;

                // 播放正确音效
                if (audioSource != null && correctMatchSound != null)
                {
                    audioSource.PlayOneShot(correctMatchSound);
                }

                // 显示正确表情
                ShowPoliceExpression(true);

                // 增加分数
                score += 10;
                correctCount++;

                // 触发匹配成功事件
                PieceMatched?.Invoke(true);

                // 检查是否完成当前画作
                if (matchedCount >= totalPieces)
                {
                    OnPaintingCompleted();
                }
            }
            else
            {
                // 匹配失败

                // 播放错误音效
                if (audioSource != null && incorrectMatchSound != null)
                {
                    audioSource.PlayOneShot(incorrectMatchSound);
                }

                // 显示错误表情
                ShowPoliceExpression(false);

                // 减少分数
                score = Mathf.Max(0, score - 5);
                errorCount++;

                // 触发匹配失败事件
                PieceMatched?.Invoke(false);
            }
        }

        /// <summary>
        /// 画作完成时调用
        /// </summary>
        private void OnPaintingCompleted()
        {
            Debug.Log($"画作完成: {currentPaintingIndex + 1}/{selectedPaintings.Count}");

            // 播放完成音效
            if (audioSource != null && taskCompleteSound != null)
            {
                audioSource.PlayOneShot(taskCompleteSound);
            }

            // 触发画作完成事件
            PaintingCompleted?.Invoke(currentPaintingIndex + 1, selectedPaintings.Count);

            // 更新当前画作索引，确保游戏结束时判定为成功
            currentPaintingIndex++;

            // 由于我们只有一个画作，完成后直接结束游戏
            StartCoroutine(DelayedEndGameForSinglePainting(1.5f));
        }

        /// <summary>
        /// 延迟结束游戏（单画作版本）
        /// </summary>
        private IEnumerator DelayedEndGameForSinglePainting(float delay)
        {
            yield return new WaitForSeconds(delay);

            // 清理当前画作
            ClearCurrentPainting();

            // 结束游戏
            EndGame();
        }



        // 警察表情子对象引用
        private GameObject correctExpressionObject;
        private GameObject incorrectExpressionObject;
        private GameObject idleExpressionObject;
        private GameObject endingExpressionObject;

        // 用于跟踪当前的延迟恢复协程
        private Coroutine idleExpressionCoroutine;

        /// <summary>
        /// 初始化警察表情对象引用
        /// </summary>
        private void InitPoliceExpressionObjects()
        {
            Debug.Log("InitPoliceExpressionObjects 开始执行");

            try
            {
                // 确保policeExpressionsObject不为空
                if (policeExpressionsObject == null)
                {
                    Debug.LogWarning("警察表情对象(policeExpressionsObject)为空，尝试查找");

                    // 尝试查找PoliceExpressions对象
                    policeExpressionsObject = GameObject.Find("PoliceExpressions");
                    Debug.Log($"GameObject.Find(\"PoliceExpressions\") 结果: {(policeExpressionsObject != null ? policeExpressionsObject.name : "null")}");

                    // 如果找不到，尝试查找包含"police"或"expression"的对象
                    if (policeExpressionsObject == null)
                    {
                        Debug.LogWarning("尝试查找包含'police'或'expression'的对象");
                        GameObject[] allObjects = GameObject.FindObjectsOfType<GameObject>();
                        Debug.Log($"场景中共有 {allObjects.Length} 个GameObject");

                        foreach (var obj in allObjects)
                        {
                            string objName = obj.name.ToLower();
                            if (objName.Contains("police") || objName.Contains("expression"))
                            {
                                Debug.Log($"找到可能的警察表情对象: {obj.name}");
                                policeExpressionsObject = obj;
                                break;
                            }
                        }
                    }

                    if (policeExpressionsObject == null)
                    {
                        Debug.LogError("无法找到PoliceExpressions对象，无法初始化表情子对象");
                        return;
                    }
                    else
                    {
                        Debug.Log($"成功找到PoliceExpressions对象: {policeExpressionsObject.name}");
                    }
                }
                else
                {
                    Debug.Log($"警察表情对象已存在: {policeExpressionsObject.name}");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"查找警察表情对象时发生异常: {ex.Message}");
                Debug.LogError($"异常堆栈: {ex.StackTrace}");
                return;
            }

            Debug.Log($"初始化警察表情子对象，父对象: {policeExpressionsObject.name}，子对象数量: {policeExpressionsObject.transform.childCount}");

            // 输出所有子对象名称，帮助调试
            for (int i = 0; i < policeExpressionsObject.transform.childCount; i++)
            {
                Transform child = policeExpressionsObject.transform.GetChild(i);
                Debug.Log($"子对象[{i}]: {child.name}");
            }

            // 尝试多种可能的名称
            string[] correctNames = { "correctExpression", "CorrectExpression", "happy", "Happy", "right", "Right" };
            string[] incorrectNames = { "incorrectExpression", "IncorrectExpression", "sad", "Sad", "wrong", "Wrong", "confused", "Confused" };
            string[] idleNames = { "idleExpression", "IdleExpression", "idle", "Idle", "normal", "Normal", "default", "Default" };
            string[] endingNames = { "endingExpression", "EndingExpression", "ending", "Ending", "complete", "Complete", "finish", "Finish" };

            // 查找正确表情对象
            correctExpressionObject = FindChildByNames(policeExpressionsObject, correctNames);
            if (correctExpressionObject != null)
                Debug.Log($"找到正确表情对象: {correctExpressionObject.name}");
            else
                Debug.LogError("未找到正确表情对象");

            // 查找错误表情对象
            incorrectExpressionObject = FindChildByNames(policeExpressionsObject, incorrectNames);
            if (incorrectExpressionObject != null)
                Debug.Log($"找到错误表情对象: {incorrectExpressionObject.name}");
            else
                Debug.LogError("未找到错误表情对象");

            // 查找空闲表情对象
            idleExpressionObject = FindChildByNames(policeExpressionsObject, idleNames);
            if (idleExpressionObject != null)
                Debug.Log($"找到空闲表情对象: {idleExpressionObject.name}");
            else
                Debug.LogError("未找到空闲表情对象");

            // 查找结束表情对象
            endingExpressionObject = FindChildByNames(policeExpressionsObject, endingNames);
            if (endingExpressionObject != null)
                Debug.Log($"找到结束表情对象: {endingExpressionObject.name}");
            else
                Debug.LogError("未找到结束表情对象");

            // 如果仍然没有找到，使用前三个子对象
            if (correctExpressionObject == null && policeExpressionsObject.transform.childCount > 0)
            {
                correctExpressionObject = policeExpressionsObject.transform.GetChild(0).gameObject;
                Debug.Log($"使用第一个子对象作为正确表情: {correctExpressionObject.name}");
            }

            if (incorrectExpressionObject == null && policeExpressionsObject.transform.childCount > 1)
            {
                incorrectExpressionObject = policeExpressionsObject.transform.GetChild(1).gameObject;
                Debug.Log($"使用第二个子对象作为错误表情: {incorrectExpressionObject.name}");
            }

            if (idleExpressionObject == null && policeExpressionsObject.transform.childCount > 2)
            {
                idleExpressionObject = policeExpressionsObject.transform.GetChild(2).gameObject;
                Debug.Log($"使用第三个子对象作为空闲表情: {idleExpressionObject.name}");
            }

            // 如果找不到结束表情对象，使用正确表情对象作为备用
            if (endingExpressionObject == null)
            {
                if (correctExpressionObject != null)
                {
                    endingExpressionObject = correctExpressionObject;
                    Debug.Log($"使用正确表情对象作为结束表情: {endingExpressionObject.name}");
                }
                else if (policeExpressionsObject.transform.childCount > 3)
                {
                    endingExpressionObject = policeExpressionsObject.transform.GetChild(3).gameObject;
                    Debug.Log($"使用第四个子对象作为结束表情: {endingExpressionObject.name}");
                }
            }

            // 设置初始状态：只显示空闲表情
            if (correctExpressionObject != null)
            {
                correctExpressionObject.SetActive(false);
                Debug.Log($"已停用正确表情: {correctExpressionObject.name}");
            }

            if (incorrectExpressionObject != null)
            {
                incorrectExpressionObject.SetActive(false);
                Debug.Log($"已停用错误表情: {incorrectExpressionObject.name}");
            }

            if (idleExpressionObject != null)
            {
                idleExpressionObject.SetActive(true);
                Debug.Log($"已激活空闲表情: {idleExpressionObject.name}");
            }

            if (endingExpressionObject != null)
            {
                endingExpressionObject.SetActive(false);
                Debug.Log($"已停用结束表情: {endingExpressionObject.name}");
            }

            // 输出最终状态
            Debug.Log($"表情对象初始化完成: correctExpression={correctExpressionObject != null}, incorrectExpression={incorrectExpressionObject != null}, idleExpression={idleExpressionObject != null}");
        }

        /// <summary>
        /// 根据多个可能的名称查找子对象
        /// </summary>
        private GameObject FindChildByNames(GameObject parent, string[] possibleNames)
        {
            // 先尝试直接查找
            foreach (string name in possibleNames)
            {
                Transform child = parent.transform.Find(name);
                if (child != null)
                {
                    Debug.Log($"通过直接查找找到子对象: {name}");
                    return child.gameObject;
                }
            }

            // 如果直接查找失败，尝试遍历所有子对象，查找名称包含关键字的对象
            for (int i = 0; i < parent.transform.childCount; i++)
            {
                Transform child = parent.transform.GetChild(i);
                string childName = child.name.ToLower();

                foreach (string name in possibleNames)
                {
                    if (childName.Contains(name.ToLower()))
                    {
                        Debug.Log($"通过关键字匹配找到子对象: {child.name}，匹配关键字: {name}");
                        return child.gameObject;
                    }
                }
            }

            return null;
        }



        /// <summary>
        /// 显示警察表情
        /// </summary>
        private void ShowPoliceExpression(bool isCorrect)
        {
            // 确保policeExpressionsObject不为空
            if (policeExpressionsObject == null)
            {
                Debug.LogWarning("警察表情对象(policeExpressionsObject)为空，无法显示表情");
                return;
            }

            // 确保表情对象不为空
            if (correctExpressionObject == null || incorrectExpressionObject == null || idleExpressionObject == null)
            {
                Debug.LogWarning("警察表情子对象未正确初始化，无法显示表情");
                return;
            }

            Debug.Log($"显示警察表情: {(isCorrect ? "正确" : "错误")}");

            try
            {
                // 停止之前的延迟恢复协程（如果有）
                if (idleExpressionCoroutine != null)
                {
                    StopCoroutine(idleExpressionCoroutine);
                    idleExpressionCoroutine = null;
                }

                // 隐藏所有表情
                correctExpressionObject.SetActive(false);
                incorrectExpressionObject.SetActive(false);
                idleExpressionObject.SetActive(false);

                // 显示对应表情
                if (isCorrect)
                {
                    correctExpressionObject.SetActive(true);
                }
                else
                {
                    incorrectExpressionObject.SetActive(true);
                }

                // 延迟恢复空闲表情
                idleExpressionCoroutine = StartCoroutine(DelayedShowIdleExpression(1f));
            }
            catch (Exception ex)
            {
                Debug.LogError($"显示警察表情时发生异常: {ex.Message}");
                Debug.LogError($"异常堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 延迟显示空闲表情
        /// </summary>
        private IEnumerator DelayedShowIdleExpression(float delay)
        {
            yield return new WaitForSeconds(delay);

            // 确保表情对象不为空
            if (correctExpressionObject == null || incorrectExpressionObject == null || idleExpressionObject == null)
            {
                Debug.LogWarning("警察表情子对象未正确初始化，无法显示空闲表情");
                yield break;
            }

            try
            {
                // 隐藏所有表情
                correctExpressionObject.SetActive(false);
                incorrectExpressionObject.SetActive(false);
                if (endingExpressionObject != null)
                {
                    endingExpressionObject.SetActive(false);
                }

                // 显示空闲表情
                idleExpressionObject.SetActive(true);

                // 清除协程引用
                idleExpressionCoroutine = null;
            }
            catch (Exception ex)
            {
                Debug.LogError($"显示空闲表情时发生异常: {ex.Message}");
                Debug.LogError($"异常堆栈: {ex.StackTrace}");

                // 清除协程引用
                idleExpressionCoroutine = null;
            }
        }

        /// <summary>
        /// 显示结束表情
        /// </summary>
        private void ShowEndingExpression()
        {
            // 确保policeExpressionsObject不为空
            if (policeExpressionsObject == null)
            {
                Debug.LogWarning("警察表情对象(policeExpressionsObject)为空，无法显示结束表情");
                return;
            }

            // 确保表情对象不为空
            if (endingExpressionObject == null)
            {
                Debug.LogWarning("结束表情对象未正确初始化，无法显示结束表情");
                return;
            }

            Debug.Log("显示警察结束表情");

            try
            {
                // 停止之前的延迟恢复协程（如果有）
                if (idleExpressionCoroutine != null)
                {
                    StopCoroutine(idleExpressionCoroutine);
                    idleExpressionCoroutine = null;
                }

                // 隐藏所有表情
                if (correctExpressionObject != null)
                    correctExpressionObject.SetActive(false);

                if (incorrectExpressionObject != null)
                    incorrectExpressionObject.SetActive(false);

                if (idleExpressionObject != null)
                    idleExpressionObject.SetActive(false);

                // 显示结束表情
                endingExpressionObject.SetActive(true);
                Debug.Log($"已激活结束表情: {endingExpressionObject.name}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"显示结束表情时发生异常: {ex.Message}");
                Debug.LogError($"异常堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 使用提示
        /// </summary>
        public override void UseHint()
        {
            // 增加提示使用次数
            hintsUsed++;

            // 减少分数
            score = Mathf.Max(0, score - 5);

            // 在实际项目中，这里应该实现提示逻辑
            // 例如：高亮显示一个正确的匹配位置
            Debug.Log("使用提示");
        }

        /// <summary>
        /// 游戏结束时调用
        /// </summary>
        protected override void OnGameEnded(bool success)
        {
            Debug.Log($"图片拼图游戏结束: {(success ? "成功" : "失败")}");

            // 播放结束音效
            if (audioSource != null)
            {
                AudioClip clip = success ? taskCompleteSound : taskFailedSound;
                if (clip != null)
                {
                    audioSource.PlayOneShot(clip);
                }
            }

            // 显示结束表情
            ShowEndingExpression();

            // 激活彩带效果对象（如果已设置）
            if (ribbonEndingEffect != null)
            {
                Debug.Log("激活彩带效果对象");
                ribbonEndingEffect.SetActive(true);
            }
            else
            {
                Debug.LogWarning("彩带效果对象未设置，请在Inspector中设置ribbonEndingEffect变量");
            }

            // 设置游戏状态
            currentGameplayState = GameplayState.Completed;
            isGameActive = false;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 清理游戏内容
        /// </summary>
        private void ClearGameContent()
        {
            // 清理当前画作
            ClearCurrentPainting();

            // 重置游戏状态
            currentPaintingIndex = 0;
            matchedCount = 0;
            totalPieces = 0;
            showingEmptyFrame = false;
        }

        /// <summary>
        /// 清理当前画作
        /// </summary>
        private void ClearCurrentPainting()
        {
            // 清理画作对象
            foreach (var paintingObject in paintingObjects)
            {
                Destroy(paintingObject);
            }
            paintingObjects.Clear();

            // 清理缺失部分对象
            foreach (var pieceObject in pieceObjects)
            {
                Destroy(pieceObject);
            }
            pieceObjects.Clear();
        }

        /// <summary>
        /// 获取本地化消息
        /// </summary>
        private string GetLocalizedMessage(Dictionary<string, string> messages)
        {
            // 在实际项目中，这里应该根据当前语言获取对应的消息
            // 例如：根据系统语言或用户设置选择合适的语言

            // 默认使用中文
            if (messages.TryGetValue("zh_CN", out string message))
            {
                return message;
            }

            // 如果没有中文，使用英文
            if (messages.TryGetValue("en", out message))
            {
                return message;
            }

            // 如果没有任何语言，返回空字符串
            return string.Empty;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected override void ReleaseResources()
        {
            // 清理游戏内容
            ClearGameContent();

            // 清理音效
            correctMatchSound = null;
            incorrectMatchSound = null;
            taskCompleteSound = null;
            taskFailedSound = null;
            missingPaintingSound = null;

            // 清理UI元素
            if (soundButton != null)
            {
                Destroy(soundButton);
                soundButton = null;
            }

            if (policeExpressionsObject != null)
            {
                Destroy(policeExpressionsObject);
                policeExpressionsObject = null;
            }

            // 清理警察表情子对象引用
            correctExpressionObject = null;
            incorrectExpressionObject = null;
            idleExpressionObject = null;
        }

        /// <summary>
        /// 计算星级
        /// </summary>
        protected override int CalculateStars()
        {
            // 根据分数和错误次数计算星级
            if (score >= 90 && errorCount <= totalPieces / 4)
                return 3;
            if (score >= 70 && errorCount <= totalPieces / 2)
                return 2;
            if (score >= 50)
                return 1;
            return 0;
        }

        /// <summary>
        /// 判断游戏是否成功
        /// </summary>
        protected override bool IsGameSuccessful()
        {
            // 如果显示了空画框，则游戏成功
            if (showingEmptyFrame)
                return true;

            // 否则，检查是否完成了所有画作
            return currentPaintingIndex >= selectedPaintings.Count;
        }

        /// <summary>
        /// 检查是否为个人最佳分数
        /// </summary>
        protected override bool IsPersonalBest(int currentScore)
        {
            // 简化实现，直接调用基类方法
            return base.IsPersonalBest(currentScore);
        }

        /// <summary>
        /// 当对象被销毁时调用
        /// </summary>
        private void OnDestroy()
        {
            Debug.Log("PictureShapePuzzleTemplate被销毁");

            // 注意：不再在这里调用PuzzlePiecesManager.DestroyManager()
            // 避免与PuzzleSceneController.OnDestroy中的调用重复
        }

        /// <summary>
        /// 获取游戏结果
        /// </summary>
        /// <returns>游戏结果对象</returns>
        public override AIMGameFramework.Core.Interfaces.GameResult GetGameResult()
        {
            try
            {
                // 计算星级
                int stars = CalculateStars();

                // 检查是否为个人最佳
                bool isPersonalBest = IsPersonalBest(score);

                // 创建游戏结果对象
                return new AIMGameFramework.Core.Interfaces.GameResult
                {
                    Success = IsGameSuccessful(),
                    Score = score,
                    Stars = stars,
                    TimeUsed = gameTime,
                    CorrectCount = correctCount,
                    ErrorCount = errorCount,
                    HintCount = hintsUsed,
                    IsPersonalBest = isPersonalBest
                };
            }
            catch (Exception ex)
            {
                Debug.LogError($"获取游戏结果时发生异常: {ex.Message}");
                Debug.LogError($"异常堆栈: {ex.StackTrace}");

                // 返回一个基本的结果对象，避免空引用异常
                return new AIMGameFramework.Core.Interfaces.GameResult
                {
                    Success = false,
                    Score = 0,
                    Stars = 0,
                    TimeUsed = 0,
                    CorrectCount = 0,
                    ErrorCount = 0,
                    HintCount = 0,
                    IsPersonalBest = false
                };
            }
        }

        #endregion
    }
}