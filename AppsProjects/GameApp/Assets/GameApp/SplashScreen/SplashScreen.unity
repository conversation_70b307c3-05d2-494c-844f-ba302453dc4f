%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientEquatorColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientGroundColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &4
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 1
    m_BakeResolution: 50
    m_AtlasSize: 1024
    m_AO: 1
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 0
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 1
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 497004113}
--- !u!196 &5
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666666
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1001 &337562675
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1631640605}
    m_Modifications:
    - target: {fileID: 4017973735614442, guid: ae44a562e74cb4f20928062b84bf8f23, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4017973735614442, guid: ae44a562e74cb4f20928062b84bf8f23, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4017973735614442, guid: ae44a562e74cb4f20928062b84bf8f23, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4017973735614442, guid: ae44a562e74cb4f20928062b84bf8f23, type: 3}
      propertyPath: m_LocalPosition.z
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 4017973735614442, guid: ae44a562e74cb4f20928062b84bf8f23, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4017973735614442, guid: ae44a562e74cb4f20928062b84bf8f23, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4017973735614442, guid: ae44a562e74cb4f20928062b84bf8f23, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4017973735614442, guid: ae44a562e74cb4f20928062b84bf8f23, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114334505047297844, guid: ae44a562e74cb4f20928062b84bf8f23,
        type: 3}
      propertyPath: loadingText
      value: 
      objectReference: {fileID: 396118220}
    - target: {fileID: 114334505047297844, guid: ae44a562e74cb4f20928062b84bf8f23,
        type: 3}
      propertyPath: defaultThemeId
      value: P
      objectReference: {fileID: 0}
    - target: {fileID: 114334505047297844, guid: ae44a562e74cb4f20928062b84bf8f23,
        type: 3}
      propertyPath: mainMenuSceneName
      value: FD1_HomePage
      objectReference: {fileID: 0}
    - target: {fileID: 114786491652609694, guid: ae44a562e74cb4f20928062b84bf8f23,
        type: 3}
      propertyPath: _anchorCamera
      value: 
      objectReference: {fileID: 1631640610}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 4017973735614442, guid: ae44a562e74cb4f20928062b84bf8f23,
        type: 3}
      insertIndex: 0
      addedObject: {fileID: 396118221}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: ae44a562e74cb4f20928062b84bf8f23, type: 3}
--- !u!4 &385770307 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4017973735614442, guid: ae44a562e74cb4f20928062b84bf8f23,
    type: 3}
  m_PrefabInstance: {fileID: 337562675}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &396118220
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 396118221}
  - component: {fileID: 396118222}
  m_Layer: 0
  m_Name: LoadingText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &396118221
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 396118220}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 385770307}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &396118222
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 396118220}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: cb457cb059b884a55a54503fb1084aad, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.77, y: 0.76}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!850595691 &497004113
LightingSettings:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Settings.lighting
  serializedVersion: 6
  m_GIWorkflowMode: 1
  m_EnableBakedLightmaps: 1
  m_EnableRealtimeLightmaps: 0
  m_RealtimeEnvironmentLighting: 1
  m_BounceScale: 1
  m_AlbedoBoost: 1
  m_IndirectOutputScale: 1
  m_UsingShadowmask: 0
  m_BakeBackend: 0
  m_LightmapMaxSize: 1024
  m_BakeResolution: 50
  m_Padding: 2
  m_LightmapCompression: 0
  m_AO: 1
  m_AOMaxDistance: 1
  m_CompAOExponent: 1
  m_CompAOExponentDirect: 0
  m_ExtractAO: 0
  m_MixedBakeMode: 1
  m_LightmapsBakeMode: 1
  m_FilterMode: 1
  m_LightmapParameters: {fileID: 15204, guid: 0000000000000000f000000000000000, type: 0}
  m_ExportTrainingData: 0
  m_TrainingDataDestination: TrainingData
  m_RealtimeResolution: 1
  m_ForceWhiteAlbedo: 0
  m_ForceUpdates: 0
  m_FinalGather: 0
  m_FinalGatherRayCount: 256
  m_FinalGatherFiltering: 1
  m_PVRCulling: 1
  m_PVRSampling: 1
  m_PVRDirectSampleCount: 32
  m_PVRSampleCount: 512
  m_PVREnvironmentSampleCount: 512
  m_PVREnvironmentReferencePointCount: 2048
  m_LightProbeSampleCountMultiplier: 4
  m_PVRBounces: 2
  m_PVRMinBounces: 2
  m_PVREnvironmentImportanceSampling: 0
  m_PVRFilteringMode: 2
  m_PVRDenoiserTypeDirect: 0
  m_PVRDenoiserTypeIndirect: 0
  m_PVRDenoiserTypeAO: 0
  m_PVRFilterTypeDirect: 0
  m_PVRFilterTypeIndirect: 0
  m_PVRFilterTypeAO: 0
  m_PVRFilteringGaussRadiusDirect: 1
  m_PVRFilteringGaussRadiusIndirect: 5
  m_PVRFilteringGaussRadiusAO: 2
  m_PVRFilteringAtrousPositionSigmaDirect: 0.5
  m_PVRFilteringAtrousPositionSigmaIndirect: 2
  m_PVRFilteringAtrousPositionSigmaAO: 1
  m_PVRTiledBaking: 0
  m_NumRaysToShootPerTexel: -1
  m_RespectSceneVisibilityWhenBakingGI: 0
--- !u!1001 &695704568
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 415592, guid: 9fad20450ad154309b3b3e4e4930fde7, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 415592, guid: 9fad20450ad154309b3b3e4e4930fde7, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.004999995
      objectReference: {fileID: 0}
    - target: {fileID: 415592, guid: 9fad20450ad154309b3b3e4e4930fde7, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 415592, guid: 9fad20450ad154309b3b3e4e4930fde7, type: 3}
      propertyPath: m_LocalPosition.z
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 415592, guid: 9fad20450ad154309b3b3e4e4930fde7, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 415592, guid: 9fad20450ad154309b3b3e4e4930fde7, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 415592, guid: 9fad20450ad154309b3b3e4e4930fde7, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 415592, guid: 9fad20450ad154309b3b3e4e4930fde7, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 224188328914685282, guid: 9fad20450ad154309b3b3e4e4930fde7,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 9fad20450ad154309b3b3e4e4930fde7, type: 3}
--- !u!1 &1631640604
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1631640605}
  - component: {fileID: 1631640610}
  - component: {fileID: 1631640609}
  - component: {fileID: 1631640608}
  - component: {fileID: 1631640606}
  m_Layer: 0
  m_Name: tk2dCamera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1631640605
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1631640604}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 385770307}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!81 &1631640606
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1631640604}
  m_Enabled: 1
--- !u!124 &1631640608
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1631640604}
  m_Enabled: 1
--- !u!114 &1631640609
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1631640604}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d60c017246963b14c9806a06670568b5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 1
  cameraSettings:
    projection: 0
    orthographicSize: 10
    orthographicPixelsPerMeter: 100
    orthographicOrigin: 1
    orthographicType: 0
    transparencySortMode: 0
    fieldOfView: 60
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
  resolutionOverride:
  - name: 16:9(1280X720)
    matchBy: 0
    width: 1280
    height: 720
    aspectRatioNumerator: 16
    aspectRatioDenominator: 9
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 6
    fitMode: 1
  - name: 16:9(1920X1080)
    matchBy: 0
    width: 1920
    height: 1080
    aspectRatioNumerator: 4
    aspectRatioDenominator: 3
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  - name: 16:9(854X480)
    matchBy: 0
    width: 854
    height: 480
    aspectRatioNumerator: 4
    aspectRatioDenominator: 3
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  - name: 16:9(1024X576)
    matchBy: 0
    width: 1024
    height: 576
    aspectRatioNumerator: 4
    aspectRatioDenominator: 3
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  - name: 16:9(1600X900)
    matchBy: 0
    width: 1600
    height: 900
    aspectRatioNumerator: 4
    aspectRatioDenominator: 3
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  - name: 16:9(960X540)
    matchBy: 0
    width: 960
    height: 540
    aspectRatioNumerator: 4
    aspectRatioDenominator: 3
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  - name: 16:9(1136X640)
    matchBy: 0
    width: 1136
    height: 640
    aspectRatioNumerator: 4
    aspectRatioDenominator: 3
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  - name: 16:9(1334X750)
    matchBy: 0
    width: 1334
    height: 750
    aspectRatioNumerator: 4
    aspectRatioDenominator: 3
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  - name: "16\uFF1A9"
    matchBy: 1
    width: 2048
    height: 1536
    aspectRatioNumerator: 16
    aspectRatioDenominator: 9
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  - name: '4:3 '
    matchBy: 1
    width: 0
    height: 0
    aspectRatioNumerator: 4
    aspectRatioDenominator: 3
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  - name: 16:10
    matchBy: 1
    width: 16
    height: 10
    aspectRatioNumerator: 16
    aspectRatioDenominator: 10
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  - name: 3:2
    matchBy: 1
    width: 0
    height: 0
    aspectRatioNumerator: 3
    aspectRatioDenominator: 2
    scale: 0.5
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  - name: 5:4
    matchBy: 1
    width: 0
    height: 0
    aspectRatioNumerator: 5
    aspectRatioDenominator: 4
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  - name: 17:10
    matchBy: 1
    width: 0
    height: 0
    aspectRatioNumerator: 17
    aspectRatioDenominator: 10
    scale: 0.8
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 7
    fitMode: 1
  - name: 15:9
    matchBy: 1
    width: 800
    height: 480
    aspectRatioNumerator: 15
    aspectRatioDenominator: 9
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  - name: 1024X600
    matchBy: 0
    width: 1024
    height: 600
    aspectRatioNumerator: 4
    aspectRatioDenominator: 3
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  inheritSettings: {fileID: 0}
  nativeResolutionWidth: 1280
  nativeResolutionHeight: 720
  _unityCamera: {fileID: 1631640610}
  viewportClippingEnabled: 0
  viewportRegion: {x: 0, y: 0, z: 100, w: 100}
  zoomFactor: 1
  forceResolutionInEditor: 1
  forceResolution: {x: 2688, y: 1242}
--- !u!20 &1631640610
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1631640604}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 1, g: 1, b: 1, a: 1}
  m_projectionMatrixMode: 0
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 480
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 0
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!1001 &1683810384
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4046965518758604, guid: 07d8bbff695c440d1bd0a59350e4d9eb, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 4046965518758604, guid: 07d8bbff695c440d1bd0a59350e4d9eb, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.6746714
      objectReference: {fileID: 0}
    - target: {fileID: 4046965518758604, guid: 07d8bbff695c440d1bd0a59350e4d9eb, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1.7622421
      objectReference: {fileID: 0}
    - target: {fileID: 4046965518758604, guid: 07d8bbff695c440d1bd0a59350e4d9eb, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.049999997
      objectReference: {fileID: 0}
    - target: {fileID: 4046965518758604, guid: 07d8bbff695c440d1bd0a59350e4d9eb, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4046965518758604, guid: 07d8bbff695c440d1bd0a59350e4d9eb, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4046965518758604, guid: 07d8bbff695c440d1bd0a59350e4d9eb, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4046965518758604, guid: 07d8bbff695c440d1bd0a59350e4d9eb, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 07d8bbff695c440d1bd0a59350e4d9eb, type: 3}
--- !u!1001 &2101654989
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 444458, guid: 594dcf324847245d8b17cee9f7eef0ed, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 444458, guid: 594dcf324847245d8b17cee9f7eef0ed, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.31448814
      objectReference: {fileID: 0}
    - target: {fileID: 444458, guid: 594dcf324847245d8b17cee9f7eef0ed, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.8335733
      objectReference: {fileID: 0}
    - target: {fileID: 444458, guid: 594dcf324847245d8b17cee9f7eef0ed, type: 3}
      propertyPath: m_LocalPosition.z
      value: -10.020508
      objectReference: {fileID: 0}
    - target: {fileID: 444458, guid: 594dcf324847245d8b17cee9f7eef0ed, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 444458, guid: 594dcf324847245d8b17cee9f7eef0ed, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 444458, guid: 594dcf324847245d8b17cee9f7eef0ed, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 444458, guid: 594dcf324847245d8b17cee9f7eef0ed, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8244458, guid: 594dcf324847245d8b17cee9f7eef0ed, type: 3}
      propertyPath: m_PlayOnAwake
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8244458, guid: 594dcf324847245d8b17cee9f7eef0ed, type: 3}
      propertyPath: spreadCustomCurve.m_RotationOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8244458, guid: 594dcf324847245d8b17cee9f7eef0ed, type: 3}
      propertyPath: panLevelCustomCurve.m_RotationOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8244458, guid: 594dcf324847245d8b17cee9f7eef0ed, type: 3}
      propertyPath: reverbZoneMixCustomCurve.m_RotationOrder
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 594dcf324847245d8b17cee9f7eef0ed, type: 3}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 2101654989}
  - {fileID: 1631640605}
  - {fileID: 695704568}
  - {fileID: 1683810384}
