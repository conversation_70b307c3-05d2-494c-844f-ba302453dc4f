{"Config": {"app_id": "*********", "fgui_packageNameApp": "Paid_NewFlashcard", "fgui_packageNameParentZone": "UI_ParentZone", "fgui_packageName": "UI_ParentZone_Landscape1", "fgui_mainView": "ParentZonePage_Main", "backSceneName": "FD1_HomePage", "appHomeSceneName": "FD1_HomePage", "btnClickSoundPath": "EffectSounds/btnClick", "PrivacyPolicy": "https://www.iappsteam.com/privacy-policy", "ICP_NO-sc": "粤ICP备2023122717号-25A", "ICP_NO-tc": "粵ICP備2023122717號-25A", "ICP_NO_URL": "https://beian.miit.gov.cn/#/home", "web_backend": "https://cws1.iappsteam.com/api", "UserPrivacy_URL": {"en": "https://www.iappsteam.com/info/en/privacy-policy", "sc": "https://www.iappsteam.com/info/sc/privacy-policy", "tc": "https://www.iappsteam.com/info/tc/privacy-policy"}, "UserTerms_URL": {"en": "https://www.iappsteam.com/info/en/terms-of-use", "sc": "https://www.iappsteam.com/info/sc/terms-of-use", "tc": "https://www.iappsteam.com/info/tc/terms-of-use"}, "Middle_Ads_Area": ["app_z<PERSON><PERSON>ong", "app_jingcha", "app_zhiyerenzhi"], "Ads_OurMoreApps": ["app_shizika", "app_z<PERSON><PERSON>ong", "app_jingcha", "app_zhiyerenzhi", "app_lajifenlei", "app_xuegaoche", "app_xia<PERSON><PERSON><PERSON>ji", "app_renshijiaotonggongju"], "Left_NavButtons": ["MyAccount", "ShoppingCart", "MoreApps", "AppSetting", "FAQ", "PrivacyPolicy", "RateUs", "ContactUs"]}, "ProductIds": {"ChineseFlashCardsForBabyFullVersion": "*********"}, "FUI_PaidPart": {"fgui_packageName": "Paid_NewFlashcard", "fgui_mainView": "<PERSON><PERSON><PERSON><PERSON>"}, "Loading_Product_Data": {"text-en": "Loading Product Data ...", "text-sc": "载入产品信息中...", "text-tc": "載入產品信息中..."}, "in_the_process_of_purchasing": {"text-en": "in the process of purchasing ...", "text-sc": "购买处理中...", "text-tc": "購買處理中..."}, "Restoring": {"text-en": "Restoring...", "text-sc": "恢复购买处理中...", "text-tc": "恢復購買處理中..."}, "app_shizika": {"ios_appId": "*********", "text-en": "", "text-sc": "宝宝识字卡", "text-tc": "寶寶識字卡"}, "app_zhaobutong": {"ios_appId": "**********", "text-en": "", "text-sc": "找茬找不同", "text-tc": "找茬找不同"}, "app_jingcha": {"ios_appId": "**********", "text-en": "", "text-sc": "警察抓小偷", "text-tc": "警察抓小偷"}, "app_zhiyerenzhi": {"ios_appId": "863844431", "text-en": "", "text-sc": "宝宝职业认知", "text-tc": "寶寶職業認知"}, "app_lajifenlei": {"ios_appId": "1061502656", "text-en": "", "text-sc": "学垃圾分类", "text-tc": "學垃圾分類"}, "app_xuegaoche": {"ios_appId": "912712970", "text-en": "", "text-sc": "雪糕冰淇淋", "text-tc": "雪糕冰淇淋"}, "app_xiaofangzhishengji": {"ios_appId": "1172115178", "text-en": "", "text-sc": "消防救援直升机", "text-tc": "消防救援直升機"}, "app_renshijiaotonggongju": {"ios_appId": "1333041115", "text-en": "", "text-sc": "认识交通工具", "text-tc": "認識交通工具"}}