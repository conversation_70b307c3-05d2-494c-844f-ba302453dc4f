/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;

namespace UI_ParentZone_Landscape
{
    public class UI_ParentZone_LandscapeBinder
    {
        public static void BindAll()
        {
            UIObjectFactory.SetPackageItemExtension(UI_Panel_UserReadyLogin.URL, typeof(UI_Panel_UserReadyLogin));
            UIObjectFactory.SetPackageItemExtension(UI_Page_AppSetting1.URL, typeof(UI_Page_AppSetting1));
            UIObjectFactory.SetPackageItemExtension(UI_BtnSimple1.URL, typeof(UI_BtnSimple1));
            UIObjectFactory.SetPackageItemExtension(UI_Page_ContactUs1.URL, typeof(UI_Page_ContactUs1));
            UIObjectFactory.SetPackageItemExtension(UI_Page_AppList_Landscape.URL, typeof(UI_Page_AppList_Landscape));
            UIObjectFactory.SetPackageItemExtension(UI_Page_FAQ1.URL, typeof(UI_Page_FAQ1));
            UIObjectFactory.SetPackageItemExtension(UI_LeftNavIconSimpleButton.URL, typeof(UI_LeftNavIconSimpleButton));
            UIObjectFactory.SetPackageItemExtension(UI_Page_EmptyContent.URL, typeof(UI_Page_EmptyContent));
            UIObjectFactory.SetPackageItemExtension(UI_Btn_RoundTitle.URL, typeof(UI_Btn_RoundTitle));
            UIObjectFactory.SetPackageItemExtension(UI_Page_RateUs.URL, typeof(UI_Page_RateUs));
            UIObjectFactory.SetPackageItemExtension(UI_Btn_RateUs.URL, typeof(UI_Btn_RateUs));
            UIObjectFactory.SetPackageItemExtension(UI_Dox_ChangePassword.URL, typeof(UI_Dox_ChangePassword));
            UIObjectFactory.SetPackageItemExtension(UI_Dox_ForgetPassword.URL, typeof(UI_Dox_ForgetPassword));
            UIObjectFactory.SetPackageItemExtension(UI_ShoppingCart_Content.URL, typeof(UI_ShoppingCart_Content));
            UIObjectFactory.SetPackageItemExtension(UI_Dox_TipForAppleLogin.URL, typeof(UI_Dox_TipForAppleLogin));
            UIObjectFactory.SetPackageItemExtension(UI_Page_AppList_Landscape1.URL, typeof(UI_Page_AppList_Landscape1));
            UIObjectFactory.SetPackageItemExtension(UI_MessageBox.URL, typeof(UI_MessageBox));
            UIObjectFactory.SetPackageItemExtension(UI_Page_MoreApps.URL, typeof(UI_Page_MoreApps));
            UIObjectFactory.SetPackageItemExtension(UI_Page_FAQ.URL, typeof(UI_Page_FAQ));
            UIObjectFactory.SetPackageItemExtension(UI_Page_PrivacyPolicy.URL, typeof(UI_Page_PrivacyPolicy));
            UIObjectFactory.SetPackageItemExtension(UI_Btn_KidsPrivacy.URL, typeof(UI_Btn_KidsPrivacy));
            UIObjectFactory.SetPackageItemExtension(UI_Btn_Privacy.URL, typeof(UI_Btn_Privacy));
            UIObjectFactory.SetPackageItemExtension(UI_Page_ContactUsx.URL, typeof(UI_Page_ContactUsx));
            UIObjectFactory.SetPackageItemExtension(UI_Btn_RoundContent.URL, typeof(UI_Btn_RoundContent));
            UIObjectFactory.SetPackageItemExtension(UI_Btn_AppICPNO.URL, typeof(UI_Btn_AppICPNO));
            UIObjectFactory.SetPackageItemExtension(UI_ParentZonePage_Main_NoLogin.URL, typeof(UI_ParentZonePage_Main_NoLogin));
            UIObjectFactory.SetPackageItemExtension(UI_LeftNavIconButton.URL, typeof(UI_LeftNavIconButton));
            UIObjectFactory.SetPackageItemExtension(UI_Page_MyAccount.URL, typeof(UI_Page_MyAccount));
            UIObjectFactory.SetPackageItemExtension(UI_Page_ShoppingCart.URL, typeof(UI_Page_ShoppingCart));
            UIObjectFactory.SetPackageItemExtension(UI_Btn_RegUser_Step1.URL, typeof(UI_Btn_RegUser_Step1));
            UIObjectFactory.SetPackageItemExtension(UI_Btn_Login_Step1.URL, typeof(UI_Btn_Login_Step1));
            UIObjectFactory.SetPackageItemExtension(UI_Btn_Userterms.URL, typeof(UI_Btn_Userterms));
            UIObjectFactory.SetPackageItemExtension(UI_Dox_UserLogin.URL, typeof(UI_Dox_UserLogin));
            UIObjectFactory.SetPackageItemExtension(UI_Panel_AccountCenter.URL, typeof(UI_Panel_AccountCenter));
            UIObjectFactory.SetPackageItemExtension(UI_Dox_CreateAccount.URL, typeof(UI_Dox_CreateAccount));
            UIObjectFactory.SetPackageItemExtension(UI_Btn_Checked.URL, typeof(UI_Btn_Checked));
        }
    }
}