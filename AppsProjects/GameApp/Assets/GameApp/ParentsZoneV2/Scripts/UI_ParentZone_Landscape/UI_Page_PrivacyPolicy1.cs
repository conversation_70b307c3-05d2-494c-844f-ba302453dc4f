/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UI_ParentZone_Landscape
{
    public partial class UI_Page_PrivacyPolicy1 : GComponent
    {
        public UI_Btn_KidsPrivacy m_Btn_KidsPrivacy;
        public UI_Btn_Privacy m_Btn_Privacy;
        public UI_Btn_Userterms m_Btn_Userterms;
        public const string URL = "ui://o38drqhiiuv54y";

        public static UI_Page_PrivacyPolicy1 CreateInstance()
        {
            return (UI_Page_PrivacyPolicy1)UIPackage.CreateObject("UI_ParentZone_Landscape", "Page_PrivacyPolicy1");
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            m_Btn_KidsPrivacy = (UI_Btn_KidsPrivacy)GetChildAt(0);
            m_Btn_Privacy = (UI_Btn_Privacy)GetChildAt(1);
            m_Btn_Userterms = (UI_Btn_Userterms)GetChildAt(2);
        }
    }
}