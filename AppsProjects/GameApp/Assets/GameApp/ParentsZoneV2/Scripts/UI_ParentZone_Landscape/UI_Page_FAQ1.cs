/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UI_ParentZone_Landscape
{
    public partial class UI_Page_FAQ1 : GComponent
    {
        public GImage m_TopBar;
        public GTextField m_title;
        public GList m_html_content;
        public const string URL = "ui://o38drqhiiuv54v";

        public static UI_Page_FAQ1 CreateInstance()
        {
            return (UI_Page_FAQ1)UIPackage.CreateObject("UI_ParentZone_Landscape", "Page_FAQ1");
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            m_TopBar = (GImage)GetChildAt(0);
            m_title = (GTextField)GetChildAt(1);
            m_html_content = (GList)GetChildAt(2);
        }
    }
}