using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using FairyGUI;
using FairyGUI.Utils;
using LitJson;

public class PZ_BasePageContent : GComponent
{
    AudioClip btnClickSound;
    protected JsonData ConfigData;
    
    public override void ConstructFromXML(FairyGUI.Utils.XML cxml)
    {
        base.ConstructFromXML(cxml);
            
        //在这里继续你的初始化
        string parentZone_config_data = Resources.Load(GlobalVariable.ParentZone_dataConfig_flag).ToString();
        ConfigData = JsonMapper.ToObject(parentZone_config_data);
    }

    // PlayClickSound 方法
    public virtual void PlayClickSound()
    {
        string btnClickSound_Path = ConfigData["Config"]["btnClickSoundPath"].ToString();
        btnClickSound = Resources.Load<AudioClip>(btnClickSound_Path);
        SoundManager.PlaySFX(btnClickSound, false, 0, 1);
    }
    
    public JsonData GetConfigData()
    {
        return ConfigData;
    }
}
