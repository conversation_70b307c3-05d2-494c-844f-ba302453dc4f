using UnityEngine;
using FairyGUI;
using FairyGUI.Utils;
using MiniJSON_New;
using DG.Tweening;
using LitJson;
using System.Globalization;
using FD1_UI.FD1_LevelPage;
using System.Collections;
using UI_ParentZone_Landscape;
using AIMGameFramework;




public class PZ_HomeV2 : MonoBehaviour
{
    public string packageName = "UI_ParentZone_Landscape";
    public AudioClip btnClickSound;

    private GComponent _mainView;
    private float current_scrollPane_posX = 0;
    private GList _list;
    private JsonData app_config_data, cards_data;
    private string[] navButtons_array;
    private int cards_count;
    private string card_packageName;

    private bool isGoingNextScene = false;

    private AudioSource audioSource;

    private UI_ParentZonePage_Main_NoLogin _viewMain;

    private GObject[] _leftNavButtons;

    private GComponent _currentMainPageContainer;

    private JsonData qna_data;

    // Start is called before the first frame update
    void Start()
    {
        audioSource = gameObject.AddComponent<AudioSource>();
        audioSource.spatialBlend = 0;

        GlobalVariable.moreApps_FromPage = GlobalVariable.MoreApps_FromPage.ParentZone;

        FGUI_DataInit();

    }

    void FGUI_DataInit()
    {
        string str_app_config_data = Resources.Load("Data_ParentsZoneV2").ToString();
        app_config_data = JsonMapper.ToObject(str_app_config_data);

        //Debug.Log(category_count);

        GRoot.inst.SetContentScaleFactor(1852, 854, UIContentScaler.ScreenMatchMode.MatchWidthOrHeight);
        UIPackage package = UIPackage.AddPackage($"{packageName}");
        foreach (var item in package.dependencies)
        {
            UIPackage.AddPackage($"{item["name"]}");
        }

        UI_ParentZone_LandscapeBinder.BindAll();
        _mainView = UI_ParentZonePage_Main_NoLogin.CreateInstance();
        _mainView.fairyBatching = true;
        _mainView.SetSize(GRoot.inst.width, GRoot.inst.height);
        _mainView.AddRelation(GRoot.inst, RelationType.Size);
        GRoot.inst.AddChild(_mainView);

        _viewMain = _mainView as UI_ParentZonePage_Main_NoLogin;

        GRoot.inst.AddChild(_mainView);

        GObject _backBtn = _viewMain.m_BtnBackHome;
        _backBtn.position = GameUtility_FGUI.Offset_iPhoneX(_backBtn.position, 74);
        _backBtn.onClick.Add(() => { GoToHome(); });

        int navButtons_category_count = app_config_data["NavButtons"]["category"].Count;

        navButtons_array = new string[navButtons_category_count];

        _leftNavButtons = new GObject[navButtons_category_count];

        // 填充row_category_array数组
        for (int i = 0; i < navButtons_array.Length; i++)
        {
            navButtons_array[i] = app_config_data["NavButtons"]["category"][i].ToString();
        }

        _list = _viewMain.m_LeftNavList;

        _list.itemRenderer = RenderListItem;
        _list.numItems = navButtons_category_count;

        UIObjectFactory.SetPackageItemExtension(UIPackage.GetItemURL(packageName, "Page_FAQ"), typeof(PZ_Page_FAQ));

        //UIObjectFactory.SetPackageItemExtension(UIPackage.GetItemURL(packageName, "Page_RateUs"), typeof(PZ_Page_RateUs));

        InitMainPageContainer();

    }

    void InitMainPageContainer(string key = "ContactUs")
    {
        Debug.Log($"InitMainPageContainer: 开始初始化页面容器，key = {key}");

        _viewMain.m_PagePoint.visible = false;

        // 如果已有容器，先移除
        if (_currentMainPageContainer != null)
        {
            Debug.Log("InitMainPageContainer: 移除现有的页面容器");
            _mainView.RemoveChild(_currentMainPageContainer);
            _currentMainPageContainer = null;
        }

        if (key == "ContactUs")
        {
            Debug.Log("InitMainPageContainer: 初始化ContactUs页面");
            InitContactUsPage();
        }

        if (key == "FAQ")
        {
            Debug.Log($"InitMainPageContainer: 初始化FAQ页面，key = {key}");
            InitFAQPage();
        }

        if (key == "PrivacyPolicy")
        {
            Debug.Log($"InitMainPageContainer: 初始化FAQ页面，key = {key}");
            InitPrivacyPolicyPage();
        }

        if (key == "RateUs")
        {
            Debug.Log($"InitMainPageContainer: 初始化RateUs页面，key = {key}");
            InitRateUsPage();
        }

        if (_currentMainPageContainer != null)
        {
            Debug.Log("InitMainPageContainer: 添加页面容器到主视图");
            _mainView.AddChild(_currentMainPageContainer);
            _currentMainPageContainer.position = _viewMain.m_PagePoint.position;
            Debug.Log($"InitMainPageContainer: 设置页面容器位置: {_viewMain.m_PagePoint.position}");
        }
        else
        {
            Debug.LogError("InitMainPageContainer: _currentMainPageContainer 为 null，无法添加到主视图");
        }
    }

    void GoToHome()
    {
        AudioClip btnClip = Resources.Load<AudioClip>("EffectSounds/BtnClick1");
        SoundManager.PlaySFX(btnClip, false, 0, 1);

        StartCoroutine(GoToScene("FD1_HomePage"));
    }

    string GetStringForLanguageTitle(string key)
    {
        string title = key;

        if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
        {
            title = app_config_data[key]["zh_CN"].ToString();
        }
        else if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.TraditionalChinese)
        {
            title = app_config_data[key]["zh_TW"].ToString();
        }
        else
        {
            title = app_config_data[key]["en"].ToString();
        }

        return title;
    }

    void SetIconLine(string key, GLoader icon)
    {
        string icon_name = app_config_data[key]["icon"].ToString();
        GameUtility_FGUI.ChangeSprite(icon, packageName, icon_name);
    }


    void RenderListItem(int index, GObject obj)
    {
        UI_LeftNavIconSimpleButton item = (UI_LeftNavIconSimpleButton)obj;
        item.SetPivot(0.5f, 0.5f);

        _leftNavButtons[index] = item;

        Color bg_color = item.m_white_bg.color;
        item.m_white_bg.color = new Color(bg_color.r, bg_color.g, bg_color.b, 0.3f);
        item.m_white_bg.visible = false;

        string key = navButtons_array[index];
        GLoader icon = item.GetChild("icon").asLoader;
        string icon_name = app_config_data[key]["icon"].ToString();
        GameUtility_FGUI.ChangeSprite(icon, "UI_ParentZone", icon_name);

        if (index == 0)
        {
            item.m_white_bg.visible = true;
        }


        if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
        {
            item.m_text.text = app_config_data[key]["zh_CN"].ToString();
        }
        if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.TraditionalChinese)
        {
            item.m_text.text = app_config_data[key]["zh_TW"].ToString();
        }
        if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.English)
        {
            item.m_text.text = app_config_data[key]["en"].ToString();
        }

        // 添加点击事件
        item.onClick.Set(() =>
        {
            // 播放点击音效
            PlayClickSound();

            if (key == "MoreApps" || key == "Settings" || key == "ShoppingCart")
            {
                string sceneName = app_config_data[key]["sceneName"].ToString();
                StartCoroutine(GoToScene(sceneName));
            }
            else
            {
                InitMainPageContainer(key);
            }

            SetAllNavBg(key);
        });


    }

    void SetAllNavBg(string current_key)
    {
        for (int i = 0; i < _leftNavButtons.Length; i++)
        {
            UI_LeftNavIconSimpleButton item = (UI_LeftNavIconSimpleButton)_leftNavButtons[i];
            item.m_white_bg.visible = false;
            if (current_key == navButtons_array[i])
            {
                item.m_white_bg.visible = true;
            }
        }
    }

    IEnumerator GoToScene(string go_scene_name, bool needFade = false)
    {
        yield return new WaitForSeconds(0.3f);

        if (!isGoingNextScene)
        {
            isGoingNextScene = true;

            // 销毁所有事件监听器
            RemoveAllEventListeners();

            GRoot.inst.RemoveChild(_mainView);
            _mainView.Dispose();
            _mainView = null;
            yield return new WaitForSeconds(0.5f);
            GameUtility.GoToSceneName_NotAddSuffix(go_scene_name, needFade);
        }
    }

    IEnumerator RefreshScene()
    {
        yield return new WaitForSeconds(0.1f);

        if (!isGoingNextScene)
        {
            isGoingNextScene = true;

            // 销毁所有事件监听器
            RemoveAllEventListeners();

            GRoot.inst.RemoveChild(_mainView);
            _mainView.Dispose();
            yield return new WaitForSeconds(0.1f);
            Application.LoadLevel(Application.loadedLevelName);
        }
    }

    // 销毁所有事件监听器
    private void RemoveAllEventListeners()
    {
        // 移除返回按钮的事件监听
        if (_viewMain != null && _viewMain.m_BtnBackHome != null)
        {
            _viewMain.m_BtnBackHome.onClick.Clear();
        }

        // 移除导航按钮的事件监听
        if (_leftNavButtons != null)
        {
            for (int i = 0; i < _leftNavButtons.Length; i++)
            {
                if (_leftNavButtons[i] != null)
                {
                    _leftNavButtons[i].onClick.Clear();
                }
            }
        }

        // 移除列表项的事件监听
        if (_list != null)
        {
            for (int i = 0; i < _list.numChildren; i++)
            {
                GObject obj = _list.GetChildAt(i);
                if (obj is UI_Btn_LevelPage)
                {
                    (obj as UI_Btn_LevelPage).onClick.Clear();
                }
            }
        }

        // 移除当前页面容器中的按钮事件监听
        if (_currentMainPageContainer != null)
        {
            // 移除隐私政策页面的按钮事件
            RemoveButtonEventListener("Btn_KidsPrivacy");
            RemoveButtonEventListener("Btn_Privacy");
            RemoveButtonEventListener("Btn_Userterms");

            // 移除评分页面的按钮事件
            RemoveButtonEventListener("Btn_RateUs");

            // 移除联系我们页面的按钮事件
            RemoveButtonEventListener("Btn_AppICPNO");

            // 移除FAQ页面的html_content列表的itemRenderer
            GList html_content_list = _currentMainPageContainer.GetChild("html_content")?.asList;
            if (html_content_list != null)
            {
                html_content_list.itemRenderer = null;
                // 清理列表中已创建的所有子项的事件监听器
                for (int i = 0; i < html_content_list.numChildren; i++)
                {
                    GObject listItem = html_content_list.GetChildAt(i);
                    if (listItem != null)
                    {
                        // 清理列表项中可能存在的事件监听器
                        ClearListItemEvents(listItem);
                    }
                }
            }
        }
    }

    // 辅助方法：移除指定按钮的事件监听器
    private void RemoveButtonEventListener(string buttonName)
    {
        if (_currentMainPageContainer != null)
        {
            GObject button = _currentMainPageContainer.GetChild(buttonName);
            if (button != null && button.asButton != null)
            {
                button.asButton.onClick.Clear();
            }
        }
    }

    // 辅助方法：清理列表项中的事件监听器
    private void ClearListItemEvents(GObject listItem)
    {
        if (listItem == null) return;

        // 清理列表项本身的点击事件
        if (listItem.asButton != null)
        {
            listItem.asButton.onClick.Clear();
        }

        // 递归清理子组件的事件监听器
        if (listItem.asCom != null)
        {
            GComponent comp = listItem.asCom;
            for (int i = 0; i < comp.numChildren; i++)
            {
                GObject child = comp.GetChildAt(i);
                if (child != null && child.asButton != null)
                {
                    child.asButton.onClick.Clear();
                }
            }
        }
    }

    void SwitchLanguageSC()
    {
        GlobalVariable.osLanguage = GlobalVariable.OSLanguage.SimplifiedChinese;
        GameManager.Instance().SaveOsLanguage();
    }

    void SwitchLanguageTC()
    {
        GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
        GameManager.Instance().SaveOsLanguage();
    }

    void SwitchLanguageEN()
    {
        GlobalVariable.osLanguage = GlobalVariable.OSLanguage.English;

        GameManager.Instance().SaveOsLanguage();
    }

    void InitContactUsPage()
    {
        _currentMainPageContainer = UI_Page_ContactUs1.CreateInstance();
        _currentMainPageContainer.GetChild("Btn_AppICPNO").text = GetStringForLanguageTitle("ICP_NO");
        _currentMainPageContainer.GetChild("title").text = GetStringForLanguageTitle("ContactUs");
        GComponent view_contact_us = _currentMainPageContainer;

        if (GlobalVariable.GetDeviceOSLanguage() == "sc")
        {
            view_contact_us.GetChild("p2").visible = true;
            view_contact_us.GetChild("wechat_subscription").visible = true;

            if (GlobalVariable.GetICP_NO())
            {
                _currentMainPageContainer.GetChild("Btn_AppICPNO").visible = true;
            }
            else
            {
                _currentMainPageContainer.GetChild("Btn_AppICPNO").visible = false;
            }
        }
        else
        {
            view_contact_us.GetChild("p2").visible = false;
            view_contact_us.GetChild("wechat_subscription").visible = false;
        }
    }

    void InitFAQPage()
    {
        Debug.Log("InitFAQPage: 开始创建FAQ页面");

        // 创建FAQ页面的UI容器
        _currentMainPageContainer = UI_Page_FAQ1.CreateInstance();
        Debug.Log("InitFAQPage: UI_Page_FAQ.CreateInstance() 成功");
        _currentMainPageContainer.GetChild("title").text = GetStringForLanguageTitle("FAQ");

        TextAsset qnaConfigAsset = Resources.Load<TextAsset>("Data_QNA_V2_Config");
        if (qnaConfigAsset == null)
        {
            Debug.LogError("PZ_Page_FAQ: 无法加载 Data_QNA_V2_Config 资源文件");
            return;
        }

        string qna_config_data = qnaConfigAsset.text;
        Debug.Log($"PZ_Page_FAQ: 成功加载QNA配置数据，长度: {qna_config_data.Length}");

        qna_data = JsonMapper.ToObject(qna_config_data);
        Debug.Log("PZ_Page_FAQ: 成功解析QNA配置数据");



        GList html_content_list = _currentMainPageContainer.GetChild("html_content").asList;
        if (html_content_list == null)
        {
            Debug.LogError("PZ_Page_FAQ: 无法找到 'html_content' 列表组件");
            return;
        }
        Debug.Log("PZ_Page_FAQ: 成功获取html_content列表组件");

        //html_content_list.SetVirtual();

        int qna_ItemCount = int.Parse(qna_data["Config"]["QNA_ItemCount"].ToString());
        Debug.Log($"PZ_Page_FAQ: QNA项目数量: {qna_ItemCount}");

        html_content_list.itemRenderer = RenderListItemForQNA;
        html_content_list.numItems = qna_ItemCount;
        Debug.Log("PZ_Page_FAQ: 设置列表渲染器和项目数量完成");

    }

    void RenderListItemForQNA(int index, GObject item)
    {
        index++;

        string rowKey = $"q{index.ToString()}";

        GComponent content_item = item.asCom;

        int content_height = int.Parse(qna_data[rowKey]["height"].ToString());

        content_item.SetSize(260, content_height);

        // 获取当前语言代码
        string languageCode = GlobalVariable.GetLanguageCode();

        // 从新的JSON结构中获取标题和内容
        string title = qna_data[rowKey]["title"][languageCode].ToString();
        string content = qna_data[rowKey]["content"][languageCode].ToString();

        string str_content = "<font color=#330033 size=30 >◇ <b><u>" + title + "</u></b></font>";
        str_content += "<br/>" + content;

        GRichTextField aRichTextField = new GRichTextField();
        aRichTextField.SetSize(260, 400);
        aRichTextField.text = $"<font size =27 >{str_content}</font>";
        aRichTextField.autoSize = AutoSizeType.Height;
        aRichTextField.align = AlignType.Left;
        aRichTextField.position = new Vector2(0, 0);
        content_item.AddChild(aRichTextField);
    }

    void InitPrivacyPolicyPage()
    {
        _currentMainPageContainer = UI_Page_PrivacyPolicy1.CreateInstance();

        GButton button1 = _currentMainPageContainer.GetChild("Btn_KidsPrivacy").asButton;
        button1.text = GetStringForLanguageTitle("Btn_KidsPrivacy");
        if (app_config_data["PrivacyPolicy"]["Btn_KidsPrivacy"].ToString() == "false")
        {
            button1.visible = false;
        }
        GoToPrivacyScene(button1);

        GButton button2 = _currentMainPageContainer.GetChild("Btn_Privacy").asButton;
        button2.text = GetStringForLanguageTitle("Btn_Privacy");
        if (app_config_data["PrivacyPolicy"]["Btn_Privacy"].ToString() == "false")
        {
            button2.visible = false;
        }
        GoToPrivacyScene(button2);

        GButton button3 = _currentMainPageContainer.GetChild("Btn_Userterms").asButton;
        button3.text = GetStringForLanguageTitle("Btn_Userterms");
        if (app_config_data["PrivacyPolicy"]["Btn_Userterms"].ToString() == "false")
        {
            button3.visible = false;
        }
        GoToPrivacyScene(button3);
    }

    void GoToPrivacyScene(GButton btn)
    {
        btn.onClick.Add(() =>
        {
            PlayClickSound();

            if (btn.name == "Btn_Privacy")
            {
                // 获取当前语言代码
                string languageCode = GlobalVariable.GetLanguageCode();

                // 从多语言配置中获取隐私政策URL
                string privacyUrl = app_config_data["Btn_Privacy"]["UserPrivacy_URL"][languageCode].ToString();

                Application.OpenURL(privacyUrl);
            }
            else
            {
                //_pz_webview.ShowWebView(_webUrlUserTerms, textClose);
            }
        });
    }

    void PlayClickSound()
    {
        AudioClip btnClip = Resources.Load<AudioClip>("EffectSounds/BtnClick1");
        SoundManager.PlaySFX(btnClip, false, 0, 1);
    }

    void InitRateUsPage()
    {
        _currentMainPageContainer = UI_Page_RateUs1.CreateInstance();

        GButton button1 = _currentMainPageContainer.GetChild("Btn_RateUs").asButton;
        Debug.Log($"InitRateUsPage: {GetStringForLanguageTitle("Btn_RateUs")}");
        button1.text = GetStringForLanguageTitle("Btn_RateUs");

        button1.onClick.Add(() =>
        {
                   PlayClickSound();

                   if (Application.platform == RuntimePlatform.IPhonePlayer)
                   {
                       IAppsTeamIOSUntil.RequestAppstoreReview();
                   }
       });
    }

}
