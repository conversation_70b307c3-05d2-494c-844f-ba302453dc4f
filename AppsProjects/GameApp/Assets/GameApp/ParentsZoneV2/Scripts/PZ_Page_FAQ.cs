using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using FairyGUI;
using FairyGUI.Utils;
using LitJson;

public class PZ_Page_FAQ : PZ_BasePageContent
{
    private JsonData qna_data;
    public override void ConstructFromXML(FairyGUI.Utils.XML cxml)
    {
        base.ConstructFromXML(cxml);

            
        //在这里继续你的初始化
        Debug.Log("PZ_Page_FAQ: 开始加载QNA配置数据");

        TextAsset qnaConfigAsset = Resources.Load<TextAsset>("Data_QNA_V2_Config");
        if (qnaConfigAsset == null)
        {
            Debug.LogError("PZ_Page_FAQ: 无法加载 Data_QNA_V2_Config 资源文件");
            return;
        }

        string qna_config_data = qnaConfigAsset.text;
        Debug.Log($"PZ_Page_FAQ: 成功加载QNA配置数据，长度: {qna_config_data.Length}");

        qna_data = JsonMapper.ToObject(qna_config_data);
        Debug.Log("PZ_Page_FAQ: 成功解析QNA配置数据");

        GComponent view_content = GetChild("content").asCom;
        if (view_content == null)
        {
            Debug.LogError("PZ_Page_FAQ: 无法找到 'content' 子组件");
            return;
        }
        Debug.Log("PZ_Page_FAQ: 成功获取content组件");

        GList html_content_list = view_content.GetChild("html_content").asList;
        if (html_content_list == null)
        {
            Debug.LogError("PZ_Page_FAQ: 无法找到 'html_content' 列表组件");
            return;
        }
        Debug.Log("PZ_Page_FAQ: 成功获取html_content列表组件");

        //html_content_list.SetVirtual();

        int qna_ItemCount = int.Parse(qna_data["Config"]["QNA_ItemCount"].ToString());
        Debug.Log($"PZ_Page_FAQ: QNA项目数量: {qna_ItemCount}");

        html_content_list.itemRenderer = RenderListItemForQNA;
        html_content_list.numItems = qna_ItemCount;
        Debug.Log("PZ_Page_FAQ: 设置列表渲染器和项目数量完成");
    }
    
    void RenderListItemForQNA(int index, GObject item)
    {
        index++;

        string rowKey = $"q{index.ToString()}";

        GComponent content_item = item.asCom;

        int content_height = int.Parse(qna_data[rowKey]["height"].ToString());

        content_item.SetSize(260, content_height);
        string str_content = "<font color=#330033 size=30 >◇ <b><u>" + qna_data[rowKey][$"a{GlobalVariable.GetOSLanguageSuffix()}"].ToString() + "</u></b></font>";
        str_content += "<br/>" + qna_data[rowKey][$"b{GlobalVariable.GetOSLanguageSuffix()}"].ToString();


        GRichTextField aRichTextField = new GRichTextField();
        aRichTextField.SetSize(260, 400);
        aRichTextField.text = $"<font size =27 >{str_content}</font>";
        aRichTextField.autoSize = AutoSizeType.Height;
        aRichTextField.align = AlignType.Left;
        aRichTextField.position = new Vector2(0, 0);
        content_item.AddChild(aRichTextField);
    }
}
