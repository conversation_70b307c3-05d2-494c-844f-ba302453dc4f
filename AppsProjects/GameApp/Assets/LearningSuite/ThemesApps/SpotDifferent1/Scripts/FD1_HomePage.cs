using FairyGUI;
using FairyGUI.Utils;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Threading.Tasks;
using LitJson;
using FD1_UI.FD1_HomePage;


public class FD1_HomePage : MonoBehaviour
{
    private bool isGoingNextScene = false;
    private UI_FD1_Home _viewMain;
    private GTweener _btnPlayTweener; // 用于保存按钮缩放动画的引用
    // 用于循环播放的 Transition 引用

    // Start is called before the first frame update
    void Start()
    {
        FGUI_DataInit();
    }

    // Update is called once per frame
    private void FGUI_DataInit()
    {
        //设置适配相关

        GRoot.inst.SetContentScaleFactor(1852, 854, UIContentScaler.ScreenMatchMode.MatchWidthOrHeight);
        UIPackage package = UIPackage.AddPackage("FGUI/FD1_HomePage");
        foreach (var item in package.dependencies)
        {
            UIPackage.AddPackage($"FGUI/{item["name"]}");
        }

        FD1_HomePageBinder.BindAll();
        _viewMain = UI_FD1_Home.CreateInstance();
        _viewMain.fairyBatching = true;
        _viewMain.SetSize(GRoot.inst.width, GRoot.inst.height);
        _viewMain.AddRelation(GRoot.inst, RelationType.Size);
        GRoot.inst.AddChild(_viewMain);


        // 为 main_title1 添加掉落回弹动画
        float finalY_title1 = _viewMain.m_main_title1.y;
        _viewMain.m_main_title1.y = -_viewMain.m_main_title1.height - 400; // 初始位置设置在屏幕上方更远的位置
        _viewMain.m_main_title1.TweenMoveY(finalY_title1, 0.6f).SetEase(EaseType.BounceOut) // 使用BounceOut效果更自然
            .OnComplete(StartPlayButtonAnimation); // 标题动画完成后开始按钮动画

        _viewMain.m_Btn_Play.onClick.Add(GotoLevelPage);
        _viewMain.m_Btn_Shopping.onClick.Add(GotoPaid);
        _viewMain.m_Btn_Setting.onClick.Add(GotoSetting);
        _viewMain.m_Btn_ParentZone.onClick.Add(GotoParentZone);

        

        // 4. 获取 Transition 对象
        //    Transition 是在编辑器里定义的过渡动效，名字要跟编辑器里保持一致
        //_myTransition = _mainView.GetTransition("t0");

        // 5. 播放 Transition
        //    参数说明：forward = true 表示正向播放；播放完成后可指定回调
        //_myTransition.Play(-1, 0, 0f, 3.5f, null);
        ///_myTransition.Play();
        //_myTransition.Play(-1, 0, 0f, 3.5f, null);

        Transition rabbitTransition = _viewMain.m_rabbit_anim.GetTransition("t0");
        rabbitTransition.Play(-1, 0, 1f, -1, null);

        // 添加气球上下浮动和影子大小变化的动画
        StartBalloonAnimation();
    }

    private void StartBalloonAnimation()
    {
        // 记录初始位置和缩放比例
        float originalY = _viewMain.m_balloon.y;
        float originalRabbitY = _viewMain.m_rabbit_anim.y;
        float originalShadowScaleX = _viewMain.m_balloon_shadow.scaleX;
        float originalShadowScaleY = _viewMain.m_balloon_shadow.scaleY;

        // 设置气球上下浮动的动画 - 减小浮动幅度为±15
        _viewMain.m_balloon.TweenMoveY(originalY - 15, 1.8f)
            .SetEase(EaseType.SineInOut)
            .OnComplete(() =>
            {
                // 当气球到达最高点，返回原始位置
                _viewMain.m_balloon.TweenMoveY(originalY, 1.8f)
                    .SetEase(EaseType.SineInOut)
                    .OnComplete(() =>
                    {
                        // 动画结束后重新开始，形成循环
                        StartBalloonAnimation();
                    });
            });

        // 同步兔子的上下浮动动画与气球一致
        _viewMain.m_rabbit_anim.TweenMoveY(originalRabbitY - 15, 1.8f)
            .SetEase(EaseType.SineInOut)
            .OnComplete(() =>
            {
                _viewMain.m_rabbit_anim.TweenMoveY(originalRabbitY, 1.8f)
                    .SetEase(EaseType.SineInOut);
            });

        // 设置影子缩放动画，与气球高度变化同步 - 减小缩放变化幅度
        // 气球上升时影子轻微缩小
        _viewMain.m_balloon_shadow.TweenScale(
            new Vector2(originalShadowScaleX * 0.9f, originalShadowScaleY * 0.9f), 1.8f)
            .SetEase(EaseType.SineInOut)
            .OnComplete(() =>
            {
                // 气球下降时影子恢复原始大小
                _viewMain.m_balloon_shadow.TweenScale(
                    new Vector2(originalShadowScaleX, originalShadowScaleY), 1.8f)
                    .SetEase(EaseType.SineInOut);
            });
    }

    private void StartPlayButtonAnimation()
    {
        // 初始比例为1
        _viewMain.m_Btn_Play.SetScale(1.0f, 1.0f);

        // 创建循环的缩放动画
        _btnPlayTweener = _viewMain.m_Btn_Play.TweenScale(new Vector2(1.2f, 1.2f), 0.5f)
            .SetEase(EaseType.SineInOut)
            .OnComplete(PlayButtonAnimationLoop); // 使用回调函数创建循环
    }

    private void PlayButtonAnimationLoop()
    {
        // 判断是放大还是缩小
        bool isEnlarged = _viewMain.m_Btn_Play.scaleX > 1.0f;

        // 交替执行放大和缩小
        Vector2 targetScale = isEnlarged ? new Vector2(1.0f, 1.0f) : new Vector2(1.2f, 1.2f);
        _btnPlayTweener = _viewMain.m_Btn_Play.TweenScale(targetScale, 0.5f)
            .SetEase(EaseType.SineInOut)
            .OnComplete(PlayButtonAnimationLoop); // 循环执行
    }

    private void GotoLevelPage()
    {
        PLayClickSound();
        // 停止按钮动画
        if (_btnPlayTweener != null)
        {
            _btnPlayTweener.Kill();
            _btnPlayTweener = null;
        }

        // 重置按钮比例为1
        _viewMain.m_Btn_Play.SetScale(1.0f, 1.0f);

        // 延迟一小段时间后切换场景，给用户一个视觉反馈
        /*
        Timers.inst.Add(0.2f, 1, (object obj) =>
        {

            StartCoroutine(GoToScene("FD1_LevelPage"));
        });
*/
        StartCoroutine(GoToScene("FD1_LevelPage"));
    }

    private void GotoPaid()
    {
        PLayClickSound();

        StartCoroutine(GoToScene("FD1_Paid"));
    }

    private void GotoSetting()
    {
        PLayClickSound();

        StartCoroutine(GoToScene("FD1_Setting"));
    }

    private void GotoParentZone()
    {
        PLayClickSound();

        StartCoroutine(GoToScene("PZ_Home"));
    }

    IEnumerator GoToScene(string go_scene_name)
    {
        yield return new WaitForSeconds(0.3f);

        if (!isGoingNextScene)
        {
            isGoingNextScene = true;

            // 销毁所有事件监听器
            RemoveAllEventListeners();

            // 完整的UI销毁流程
            if (_ != null)
            {
                _mainView.RemoveFromParent();
                GRoot.inst.RemoveChild(_mainView);
                _mainView.Dispose();
                _mainView = null; // 清空引用，确保垃圾回收


            }

            // 清空其他引用
            _viewMain = null;
            _btnPlayTweener = null;

            yield return new WaitForSeconds(0.5f);
            GameUtility.GoToSceneName_NotAddSuffix(go_scene_name);

        }
    }

    // 销毁所有事件监听器
    private void RemoveAllEventListeners()
    {
        // 移除所有按钮的点击事件
        if (_viewMain != null)
        {
            if (_viewMain.m_Btn_Play != null)
            {
                _viewMain.m_Btn_Play.onClick.Clear();
            }

            if (_viewMain.m_Btn_Shopping != null)
            {
                _viewMain.m_Btn_Shopping.onClick.Clear();
            }

            if (_viewMain.m_Btn_Setting != null)
            {
                _viewMain.m_Btn_Setting.onClick.Clear();
            }

            if (_viewMain.m_Btn_ParentZone != null)
            {
                _viewMain.m_Btn_ParentZone.onClick.Clear();
            }
        }

        // 停止按钮动画
        if (_btnPlayTweener != null)
        {
            _btnPlayTweener.Kill();
            _btnPlayTweener = null;
        }

        // 停止所有UI组件的动画
        StopAllAnimations();

        // 清理计时器
        // 注意：FairyGUI的Timers是全局的，这里只能清理当前对象相关的
        // 如果有特定的计时器ID，应该在这里清理

        // 强制垃圾回收（可选，但有助于确保内存释放）
        System.GC.Collect();
    }

    // 停止所有动画的辅助方法
    private void StopAllAnimations()
    {
        if (_viewMain != null)
        {
            // 停止所有UI组件的Tween动画
            if (_viewMain.m_main_title1 != null)
            {
                GTween.Kill(_viewMain.m_main_title1, true);
            }

            if (_viewMain.m_balloon != null)
            {
                GTween.Kill(_viewMain.m_balloon, true);
            }

            if (_viewMain.m_rabbit_anim != null)
            {
                GTween.Kill(_viewMain.m_rabbit_anim, true);
            }

            if (_viewMain.m_balloon_shadow != null)
            {
                GTween.Kill(_viewMain.m_balloon_shadow, true);
            }

            // 停止Transition动画
            if (_viewMain.m_rabbit_anim != null)
            {
                Transition rabbitTransition = _viewMain.m_rabbit_anim.GetTransition("t0");
                if (rabbitTransition != null)
                {
                    rabbitTransition.Stop();
                }
            }
        }
    }

    private void PLayClickSound()
    {
        AudioClip btnClip = Resources.Load<AudioClip>("EffectSounds/BtnClick1");
        SoundManager.PlaySFX(btnClip, false, 0, 1);
    }
}